# -*- coding: utf-8 -*-
from odoo import fields, models

class ResCompany(models.Model):
    _inherit = 'res.company'

    lateness_policy = fields.Selection([
        ('none', 'Disabled'),
        ('no_show', '<PERSON> as No Show after start time'),
        ('forfeit', '<PERSON> as Forfeited before start time'),
    ], default='no_show', string="Default Lateness Policy")
    
    no_show_delay_minutes = fields.Integer(
        string="No Show Delay (Minutes)",
        default=15
    )
    
    forfeit_minutes_before = fields.Integer(
        string="Forfeit Before Start (Minutes)",
        default=15
    )