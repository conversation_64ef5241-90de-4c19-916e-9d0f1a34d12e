<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="cron_clean_prereservation" model="ir.cron">
            <field name="name">Cancel expired pre-reservations</field>
            <field name="model_id" ref="model_business_appointment_core"/>
            <field name="state">code</field>
            <field name="code">model.action_clean_expired_prereserv()</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S')" />
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">10</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="doall">False</field>
        </record>

        <record id="cron_reminder_alarm_task" model="ir.cron">
            <field name="name">Send SMS and Email Reminders</field>
            <field name="model_id" ref="model_alarm_task"/>
            <field name="state">code</field>
            <field name="code">model.action_cron_reminder()</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S')" />
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">10</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="doall">False</field>
        </record>

    </data>
</odoo>
