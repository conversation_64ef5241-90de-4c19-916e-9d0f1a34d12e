<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Appointment sequence -->
        <record id="ir_sequence_business_appointment" model="ir.sequence">
            <field name="name">Business Appointments</field>
            <field name="code">business.appointment</field>
            <field name="prefix">BA-%(year)s/</field>
            <field name="padding">3</field>
            <field name="company_id" eval="False"/>
        </record>
        <!-- New tickets -->
        <record id="mt_business_appointment_new" model="mail.message.subtype">
            <field name="name">New Appointments and Recovering</field>
            <field name="res_model">business.appointment</field>
            <field name="default" eval="False"/>
            <field name="hidden" eval="False"/>
            <field name="internal" eval="True"/>
            <field name="description">New appointment</field>
        </record>
        <record id="mt_business_resource_new" model="mail.message.subtype">
            <field name="name">New Appointments and Recovering</field>
            <field name="sequence">0</field>
            <field name="res_model">business.resource</field>
            <field name="internal" eval="True"/>
            <field name="default" eval="True"/>
            <field name="hidden" eval="False"/>
            <field name="parent_id" eval="ref('business_appointment.mt_business_appointment_new')"/>
            <field name="relation_field">resource_id</field>
            <field name="description">New appointment</field>
        </record>
        <record id="mt_business_resource_type_new" model="mail.message.subtype">
            <field name="name">New Appointments and Recovering</field>
            <field name="sequence">0</field>
            <field name="res_model">business.resource.type</field>
            <field name="internal" eval="True"/>
            <field name="default" eval="True"/>
            <field name="hidden" eval="False"/>
            <field name="parent_id" eval="ref('business_appointment.mt_business_resource_new')"/>
            <field name="relation_field">resource_type_id</field>
            <field name="description">New appointment</field>
        </record>
        <!-- Cancellation -->
        <record id="mt_business_appointment_cancel" model="mail.message.subtype">
            <field name="name">Appointments Cancellation</field>
            <field name="res_model">business.appointment</field>
            <field name="default" eval="False"/>
            <field name="hidden" eval="False"/>
            <field name="internal" eval="False"/>
            <field name="description">Cancellation</field>
        </record>
        <record id="mt_business_resource_cancel" model="mail.message.subtype">
            <field name="name">Appointments Cancellation</field>
            <field name="sequence">2</field>
            <field name="res_model">business.resource</field>
            <field name="internal" eval="False"/>
            <field name="default" eval="False"/>
            <field name="hidden" eval="False"/>
            <field name="parent_id" eval="ref('business_appointment.mt_business_appointment_cancel')"/>
            <field name="relation_field">resource_id</field>
            <field name="description">Cancellation</field>
        </record>
        <record id="mt_business_resource_type_cancel" model="mail.message.subtype">
            <field name="name">Appointments Cancellation</field>
            <field name="sequence">2</field>
            <field name="res_model">business.resource.type</field>
            <field name="internal" eval="False"/>
            <field name="default" eval="False"/>
            <field name="hidden" eval="False"/>
            <field name="parent_id" eval="ref('business_appointment.mt_business_resource_cancel')"/>
            <field name="relation_field">resource_type_id</field>
            <field name="description">Cancellation</field>
        </record>
        <!-- Reserved Time Changed -->
        <record id="mt_business_appointment_reserved_time_change" model="mail.message.subtype">
            <field name="name">Rescheduling / Resource Update</field>
            <field name="res_model">business.appointment</field>
            <field name="default" eval="False"/>
            <field name="hidden" eval="False"/>
            <field name="internal" eval="True"/>
            <field name="sequence">1</field>
            <field name="description">Appointment Update</field>
        </record>
        <record id="mt_business_resource_reserved_time_change" model="mail.message.subtype">
            <field name="name">Rescheduling / Resource Update</field>
            <field name="sequence">1</field>
            <field name="res_model">business.resource</field>
            <field name="internal" eval="True"/>
            <field name="default" eval="True"/>
            <field name="hidden" eval="False"/>
            <field name="parent_id" eval="ref('business_appointment.mt_business_appointment_reserved_time_change')"/>
            <field name="relation_field">resource_id</field>
            <field name="description">Appointment Update</field>
        </record>
        <record id="mt_business_resource_type_reserved_time_change" model="mail.message.subtype">
            <field name="name">Rescheduling / Resource Update</field>
            <field name="sequence">1</field>
            <field name="res_model">business.resource.type</field>
            <field name="internal" eval="True"/>
            <field name="default" eval="True"/>
            <field name="hidden" eval="False"/>
            <field name="parent_id" eval="ref('business_appointment.mt_business_resource_reserved_time_change')"/>
            <field name="relation_field">resource_type_id</field>
            <field name="description">Appointment Update</field>
        </record>        
        <!-- Ratings -->
        <record id="mt_appointment_rating" model="mail.message.subtype">
            <field name="name">Appointment Rating</field>
            <field name="res_model">business.appointment</field>
            <field name="default" eval="False"/>
            <field name="sequence">50</field>
            <field name="description">Ratings</field>
        </record>
        <record id="mt_business_resource_rating" model="mail.message.subtype">
            <field name="name">Appointment Rating</field>
            <field name="res_model">business.resource</field>
            <field name="default" eval="False"/>
            <field name="parent_id" eval="ref('business_appointment.mt_appointment_rating')"/>
            <field name="relation_field">resource_id</field>
            <field name="sequence">50</field>
            <field name="description">Rating Request</field>
        </record>
        <record id="mt_business_resource_type_rating" model="mail.message.subtype">
            <field name="name">Appointment Rating</field>
            <field name="res_model">business.resource.type</field>
            <field name="default" eval="False"/>
            <field name="parent_id" eval="ref('business_appointment.mt_business_resource_rating')"/>
            <field name="relation_field">resource_type_id</field>
            <field name="sequence">50</field>
            <field name="description">Rating Request</field>
        </record>
        <!-- Reminders -->
        <record id="mt_reminder_internal" model="mail.message.subtype">
            <field name="name">Internal Reminder</field>
            <field name="default" eval="False"/>
            <field name="internal" eval="True"/>
            <field name="hidden" eval="True"/>
            <field name="sequence" eval="100"/>
        </record>
        <record id="mt_reminder_external" model="mail.message.subtype">
            <field name="name">External Reminder</field>
            <field name="default" eval="False"/>
            <field name="internal" eval="False"/>
            <field name="hidden" eval="True"/>
            <field name="sequence" eval="100"/>
        </record>

    </data>
</odoo>
