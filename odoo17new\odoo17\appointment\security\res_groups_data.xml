<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="group_appointment_user" model="res.groups">
        <field name="name">User</field>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="category_id" ref="base.module_category_services_appointment"/>
    </record>

    <record id="group_appointment_manager" model="res.groups">
        <field name="name">Administrator</field>
        <field name="implied_ids" eval="[(4, ref('appointment.group_appointment_user'))]"/>
        <field name="category_id" ref="base.module_category_services_appointment"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin')), (4, ref('base.default_user'))]"/>
    </record>

</odoo>
