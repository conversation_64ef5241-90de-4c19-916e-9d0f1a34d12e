<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data noupdate="1">

        <record id="appointment_type_rule_public" model="ir.rule">
            <field name="name">Calendar: public: published only</field>
            <field name="model_id" ref="model_appointment_type"/>
            <field name="groups" eval="[(4, ref('base.group_public')), (4, ref('base.group_portal'))]"/>
            <field name="domain_force">[('website_published', '=', True)]</field>
        </record>

    </data>

</odoo>
