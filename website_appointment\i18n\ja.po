# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_appointment
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:22+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>未公開"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "<i class=\"fa fa-long-arrow-left me-2\"/>All Appointments"
msgstr "<i class=\"fa fa-long-arrow-left me-2\"/>すべての予約"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid ""
"<i class=\"fa fa-video-camera fa-fw me-1 fs-5 text-muted\"/>\n"
"                                <span class=\"o_not_editable\">Online</span>"
msgstr ""
"<i class=\"fa fa-video-camera fa-fw me-1 fs-5 text-muted\"/>\n"
"                                <span class=\"o_not_editable\">オンライン</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_pages_kanban_view
msgid "<span class=\"fa fa-globe\" title=\"website\"/>"
msgstr "<span class=\"fa fa-globe\" title=\"website\"/>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_kanban
msgid "<span class=\"text-bg-success\">Published</span>"
msgstr "<span class=\"text-bg-success\">公開</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "<span>See all availabilities <i class=\"fa fa-long-arrow-right\"/></span>"
msgstr "<span>全ての空き状況を表示<i class=\"fa fa-long-arrow-right\"/></span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a function here..."
msgstr "ここに機能を追加"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a resource description here..."
msgstr "ここにリソースの説明を追加"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a user description here..."
msgstr "ここにユーザ説明を追加..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "Add an intro message here..."
msgstr "ここに案内メッセージを追加..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All Types"
msgstr "全てのタイプ"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All assigned users"
msgstr "全ての割当済ユーザ"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
#: model:website.menu,name:website_appointment.menu_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_online_appointment_snippet
#, python-format
msgid "Appointment"
msgstr "予約"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_invite
msgid "Appointment Invite"
msgstr "アポイントメント招待"

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.action_appointment_pages_list
msgid "Appointment Pages"
msgstr "予約ページ"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_type
msgid "Appointment Type"
msgstr "予約タイプ"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
#, python-format
msgid "Appointment Type Name"
msgstr "予約タイプ名"

#. module: website_appointment
#: model:ir.ui.menu,name:website_appointment.menu_appointment_pages
#: model:ir.ui.menu,name:website_appointment.website_appointment_type_menu
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Appointments"
msgstr "予約"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointments Page"
msgstr "予約ページ"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment
msgid "Book an Appointment"
msgstr "予約をする"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Booked"
msgstr "会場予約済"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__can_publish
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__can_publish
msgid "Can Publish"
msgstr "公開可"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Card Design"
msgstr "カードデザイン"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Choose who you will meet"
msgstr "担当者を選択"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Choose your appointment"
msgstr "予約を選択"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Contact us"
msgstr "お問い合わせ"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__cover_properties
msgid "Cover Properties"
msgstr "カバー性質"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Date &amp; time"
msgstr "日付 &amp; 時間"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid ""
"Details<span class=\"d-inline-block mx-sm-3 fa fa-angle-right text-muted "
"fs-5\"/>"
msgstr ""
"詳細<span class=\"d-inline-block mx-sm-3 fa fa-angle-right text-muted fs-5\"/>"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__appointment_type_warning_msg
msgid "Different Website Message"
msgstr "異なるウェブサイトメッセージ"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Go back to Appointment"
msgstr "予約に戻る"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__is_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_published
msgid "Is Published"
msgstr "公開済"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Meeting"
msgstr "ミーティング"

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.appointment_type_action_add_simplified
msgid "New Appointment Type"
msgstr "新規予約タイプ"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "No result for \""
msgstr "次の結果がありません\""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Operator"
msgstr "担当者"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
#, python-format
msgid "Please fill this field"
msgstr "この項目にご記入ください"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_form
msgid "Publish"
msgstr "公開"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Published"
msgstr "公開済"

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_id
msgid "Restrict publishing to this website."
msgstr "このウェブサイトへの公開を制限。"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO最適化済"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
#, python-format
msgid "Schedule an appointment"
msgstr "予約をする"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_validated
msgid "Schedule another appointment"
msgstr "他の予約をスケジュール"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Search..."
msgstr "検索..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "See all appointments <i class=\"fa fa-long-arrow-right\"/>"
msgstr "全ての予約を表示<i class=\"fa fa-long-arrow-right\"/>"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__seo_name
msgid "Seo name"
msgstr "SEO名"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific Types"
msgstr "特定のタイプ"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific users"
msgstr "特定のユーザ"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/appointment_invite.py:0
#, python-format
msgid ""
"The following appointment type(s) are not compatible with the website "
"chosen: "
msgstr "次の予約タイプは、選択したウェブサイトと互換性がありません: "

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_url
msgid "The full URL to access the document through the website."
msgstr "サイト経由して、文書にアクセスする完全なURL。"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "There is currently no appointment available"
msgstr "現在利用可能な予約はありません"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_form
msgid "Unpublish"
msgstr "非公開"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_list_layout
msgid "Use the top button '<b>+ New</b>' to create an appointment type."
msgstr "上の「<b>+ 新規</b>」ボタンで、予約タイプを作成してください。"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
#, python-format
msgid "Users"
msgstr "ユーザ"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_published
msgid "Visible on current website"
msgstr "現在のサイトに表示"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_id
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Website"
msgstr "ウェブサイト"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_url
msgid "Website URL"
msgstr "サイトURL"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_description
msgid "Website meta description"
msgstr "サイトメタディスクリプション"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_keywords
msgid "Website meta keywords"
msgstr "サイトメタキーワード"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_title
msgid "Website meta title"
msgstr "サイトメタタイトル"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_og_img
msgid "Website opengraph image"
msgstr "サイトopengraph画像"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "contact us"
msgstr "お問合せ"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
#, python-format
msgid "e.g. \"Technical Demo\""
msgstr "例: 「技術デモ」"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "or"
msgstr "又は"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Specify"
msgstr "⌙ 指定する"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Users"
msgstr "⌙ ユーザ"
