diff --git a/abatch1/business_appointment/__manifest__.py b/abatch1/business_appointment/__manifest__.py
index 376a6c9..4e7b50f 100644
--- a/abatch1/business_appointment/__manifest__.py
+++ b/abatch1/business_appointment/__manifest__.py
@@ -1,7 +1,7 @@
 # -*- coding: utf-8 -*-
 {
     "name": "Universal Appointments and Time Reservations",
-    "version": "15.0.1.0.16",
+    "version": "17.0.1.0.0",
     "category": "Extra Tools",
     "author": "faOtools",
     "website": "https://faotools.com/apps/15.0/universal-appointments-and-time-reservations-607",
diff --git a/abatch1/business_appointment/data/templates.xml b/abatch1/business_appointment/data/templates.xml
index 5b7b7ac..ba6ec8f 100644
--- a/abatch1/business_appointment/data/templates.xml
+++ b/abatch1/business_appointment/data/templates.xml
@@ -40,10 +40,10 @@
                             <div>
                                 Hello 
                                 <t t-if="object.partner_id">
-                                    <t t-out="object.partner_id.name"/>,
+                                    <t t-esc="object.partner_id.name"/>,
                                 </t>
                                 <t t-else="">
-                                    <t t-out="object.contact_name"/>,
+                                    <t t-esc="object.contact_name"/>,
                                 </t>
                                 <br/>
                                 thank you! 
@@ -56,12 +56,12 @@
                                 </t>                           
                                 <br/>
                                 <ul>
-                                    <li>Reference: <t t-out="object.name  or ''"/></li>
-                                    <li>Scheduled Time: <t t-out="object.return_scheduled_time_tz(True) or ''"/></li>
-                                    <li>Resource: <t t-out="object.resource_id.name or ''"/></li>
-                                    <li>Service: <t t-out="object.service_id.name or ''"/></li>
+                                    <li>Reference: <t t-esc="object.name  or ''"/></li>
+                                    <li>Scheduled Time: <t t-esc="object.return_scheduled_time_tz(True) or ''"/></li>
+                                    <li>Resource: <t t-esc="object.resource_id.name or ''"/></li>
+                                    <li>Service: <t t-esc="object.service_id.name or ''"/></li>
                                     <li t-if="object.resource_id.location or object.service_id.location">
-                                        <t t-out="bject.service_id.location or object.resource_id.location or ''"/>
+                                        <t t-esc="bject.service_id.location or object.resource_id.location or ''"/>
                                     </li>
                                 </ul>
                             </div>
@@ -89,21 +89,21 @@
                 <tbody>
                     <tr>
                         <td valign="middle" align="left">
-                            <t t-out="ctx['target_company'].name"/>
+                            <t t-esc="ctx['target_company'].name"/>
                         </td>
                     </tr>
                 <tr>
                     <td valign="middle" align="left" style="opacity: 0.7;">
-                        <t t-out="ctx['target_company'].phone"/>
+                        <t t-esc="ctx['target_company'].phone"/>
                         <t t-if="ctx['target_company'].email">
                             | 
                             <a t-attf-href="'mailto:%s' % #{ctx['target_company'].email}" style="color: #454748;">
-                                <t t-out="ctx['target_company'].email"/>
+                                <t t-esc="ctx['target_company'].email"/>
                             </a>
                         </t>
                         | 
                         <a t-attf-href="#{ctx['website_http_domain']}" style=" color: #454748;">
-                            <t t-out="ctx['website_http_domain']"/>
+                            <t t-esc="ctx['website_http_domain']"/>
                         </a>
                     </td>
                 </tr>
@@ -131,12 +131,12 @@
     we are writing to remind about the appointment:                              
     <br/>
     <ul>
-        <li>Reference: <t t-out="object.name  or ''"/></li>
-        <li>Scheduled Time: <t t-out="object.return_scheduled_time_tz(True) or ''"/></li>
-        <li>Resource: <t t-out="object.resource_id.name or ''"/></li>
-        <li>Service: <t t-out="object.service_id.name or ''"/></li>
+        <li>Reference: <t t-esc="object.name  or ''"/></li>
+        <li>Scheduled Time: <t t-esc="object.return_scheduled_time_tz(True) or ''"/></li>
+        <li>Resource: <t t-esc="object.resource_id.name or ''"/></li>
+        <li>Service: <t t-esc="object.service_id.name or ''"/></li>
         <li t-if="object.resource_id.location or object.service_id.location">
-            <t t-out="bject.service_id.location or object.resource_id.location or ''"/>
+            <t t-esc="bject.service_id.location or object.resource_id.location or ''"/>
         </li>
     </ul>  
 </div>
@@ -164,21 +164,21 @@
                             <div>
                                 Hello 
                                 <t t-if="object.partner_id">
-                                    <t t-out="object.partner_id.name"/>,
+                                    <t t-esc="object.partner_id.name"/>,
                                 </t>
                                 <t t-else="">
-                                    <t t-out="object.contact_name"/>,
+                                    <t t-esc="object.contact_name"/>,
                                 </t>
                                 <br/>
-                                Please take a moment to rate our services related to the appointment "<strong><t t-out="object.name"/></strong>":                            
+                                Please take a moment to rate our services related to the appointment "<strong><t t-esc="object.name"/></strong>":                            
                                 <br/>
                                 <ul>
-                                    <li>Resource: <t t-out="object.resource_id.name or ''"/></li>
-                                    <li>Service: <t t-out="object.service_id.name or ''"/></li>
+                                    <li>Resource: <t t-esc="object.resource_id.name or ''"/></li>
+                                    <li>Service: <t t-esc="object.service_id.name or ''"/></li>
                                     <li t-if="object.resource_id.location or object.service_id.location">
-                                        <t t-out="bject.service_id.location or object.resource_id.location or ''"/>
+                                        <t t-esc="bject.service_id.location or object.resource_id.location or ''"/>
                                     </li>
-                                    <li>Scheduled Time: <t t-out="object.return_scheduled_time_tz(True) or ''"/></li>
+                                    <li>Scheduled Time: <t t-esc="object.return_scheduled_time_tz(True) or ''"/></li>
                                 </ul>
                             </div>
                         </td>
diff --git a/abatch1/business_appointment/reports/business_appointment_report.xml b/abatch1/business_appointment/reports/business_appointment_report.xml
index 5359e66..2a7017c 100644
--- a/abatch1/business_appointment/reports/business_appointment_report.xml
+++ b/abatch1/business_appointment/reports/business_appointment_report.xml
@@ -21,7 +21,7 @@
                          class="w-100 mt8"
                     >
                         <strong>Scheduled Time:</strong> 
-                        <span t-out="doc.return_scheduled_time_tz(True)"/> 
+                        <span t-esc="doc.return_scheduled_time_tz(True)"/> 
                     </div>
                     <div id="scheduled_time"
                          class="w-100 mt8"
diff --git a/abatch1/business_appointment/views/appointment_product.xml b/abatch1/business_appointment/views/appointment_product.xml
index df1792b..a3a1f24 100644
--- a/abatch1/business_appointment/views/appointment_product.xml
+++ b/abatch1/business_appointment/views/appointment_product.xml
@@ -171,7 +171,7 @@
                                     </div>
                                     <div class="o_kanban_primary_left ba_kanban_primary_left">
                                         <div class="o_primary ba_primary">
-                                            <span><t t-out="record.name.value"/></span>
+                                            <span><t t-esc="record.name.value"/></span>
                                         </div>
                                     </div>
                                     <div class="oe_kanban_details ba_kanban_details">
@@ -187,7 +187,7 @@
                                                        role="img"
                                                        aria-label="Percentage of satisfaction"
                                                        title="Percentage of satisfaction"/> 
-                                                    <t t-out="record.rating_satisfaction.value"/> %
+                                                    <t t-esc="record.rating_satisfaction.value"/> %
                                                 </a>
                                             </li>
                                         </ul>
diff --git a/abatch1/business_appointment/views/business_resource.xml b/abatch1/business_appointment/views/business_resource.xml
index 9ecaf7d..c76fc62 100644
--- a/abatch1/business_appointment/views/business_resource.xml
+++ b/abatch1/business_appointment/views/business_resource.xml
@@ -178,7 +178,7 @@
                                     </div>
                                     <div class="o_kanban_primary_left ba_kanban_primary_left">
                                         <div class="o_primary ba_primary">
-                                            <span><t t-out="record.name.value"/></span>
+                                            <span><t t-esc="record.name.value"/></span>
                                         </div>
                                     </div>
                                     <div class="oe_kanban_details ba_kanban_details">
@@ -201,7 +201,7 @@
                                                        role="img"
                                                        aria-label="Percentage of satisfaction"
                                                        title="Percentage of satisfaction"/> 
-                                                    <t t-out="record.rating_satisfaction.value"/> %
+                                                    <t t-esc="record.rating_satisfaction.value"/> %
                                                 </a>
                                             </li>
                                         </ul>
diff --git a/abatch1/business_appointment/views/business_resource_type.xml b/abatch1/business_appointment/views/business_resource_type.xml
index e52d0fe..f6d6b76 100644
--- a/abatch1/business_appointment/views/business_resource_type.xml
+++ b/abatch1/business_appointment/views/business_resource_type.xml
@@ -186,7 +186,7 @@
                                     </div>
                                     <div class="o_kanban_primary_left ba_kanban_primary_left">
                                         <div class="o_primary ba_primary">
-                                            <span><t t-out="record.name.value"/></span>
+                                            <span><t t-esc="record.name.value"/></span>
                                         </div>
                                     </div>
                                     <div class="oe_kanban_details ba_kanban_details">
@@ -214,7 +214,7 @@
                                                        role="img"
                                                        aria-label="Percentage of satisfaction"
                                                        title="Percentage of satisfaction"/> 
-                                                    <t t-out="record.rating_satisfaction.value"/> %
+                                                    <t t-esc="record.rating_satisfaction.value"/> %
                                                 </a>
                                             </li>
                                         </ul>
diff --git a/abatch1/business_appointment_sale/__manifest__.py b/abatch1/business_appointment_sale/__manifest__.py
index a96aba9..cfd2356 100644
--- a/abatch1/business_appointment_sale/__manifest__.py
+++ b/abatch1/business_appointment_sale/__manifest__.py
@@ -1,7 +1,7 @@
 # -*- coding: utf-8 -*-
 {
     "name": "Universal Appointments: Sales",
-    "version": "15.0.1.0.2",
+    "version": "17.0.1.0.0",
     "category": "Sales",
     "author": "faOtools",
     "website": "https://faotools.com/apps/15.0/universal-appointments-sales-612",
diff --git a/abatch1/business_appointment_time_tracking/__manifest__.py b/abatch1/business_appointment_time_tracking/__manifest__.py
index 873cabe..b30789e 100644
--- a/abatch1/business_appointment_time_tracking/__manifest__.py
+++ b/abatch1/business_appointment_time_tracking/__manifest__.py
@@ -1,7 +1,7 @@
 # -*- coding: utf-8 -*-
 {
     "name": "Universal Appointments: Time Tracking",
-    "version": "15.0.1.0.1",
+    "version": "17.0.1.0.0",
     "category": "Extra Tools",
     "author": "faOtools",
     "website": "https://faotools.com/apps/15.0/universal-appointments-time-tracking-613",
diff --git a/abatch1/business_appointment_website/__manifest__.py b/abatch1/business_appointment_website/__manifest__.py
index 723d800..aff2903 100644
--- a/abatch1/business_appointment_website/__manifest__.py
+++ b/abatch1/business_appointment_website/__manifest__.py
@@ -1,7 +1,7 @@
 # -*- coding: utf-8 -*-
 {
     "name": "Universal Appointments: Portal and Website",
-    "version": "15.0.1.0.2",
+    "version": "17.0.1.0.0",
     "category": "Website",
     "author": "faOtools",
     "website": "https://faotools.com/apps/15.0/universal-appointments-portal-and-website-611",
diff --git a/abatch1/business_appointment_website/data/template.xml b/abatch1/business_appointment_website/data/template.xml
index 615b86e..69fcc76 100644
--- a/abatch1/business_appointment_website/data/template.xml
+++ b/abatch1/business_appointment_website/data/template.xml
@@ -40,10 +40,10 @@
                             <div>
                                 Hello 
                                 <t t-if="object.partner_id">
-                                    <t t-out="object.partner_id.name"/>,
+                                    <t t-esc="object.partner_id.name"/>,
                                 </t>
                                 <t t-else="">
-                                    <t t-out="object.contact_name"/>,
+                                    <t t-esc="object.contact_name"/>,
                                 </t>
                                 <br/>
                                 Your confirmation code is:<br/>
@@ -52,7 +52,7 @@
                                         <tr>
                                             <td>   
                                                 <strong>
-                                                    <t t-out="object.confirmation_code"/>
+                                                    <t t-esc="object.confirmation_code"/>
                                                 </strong>
                                             </td>
                                         </tr>
@@ -84,21 +84,21 @@
                 <tbody>
                     <tr>
                         <td valign="middle" align="left">
-                            <t t-out="object.website_id.company_id.name"/>
+                            <t t-esc="object.website_id.company_id.name"/>
                         </td>
                     </tr>
                 <tr>
                     <td valign="middle" align="left" style="opacity: 0.7;">
-                        <t t-out="object.website_id.company_id.phone"/>
+                        <t t-esc="object.website_id.company_id.phone"/>
                         <t t-if="object.website_id.company_id.email">
                             | 
                             <a t-attf-href="'mailto:%s' % #{object.website_id.company_id.email}" style="color: #454748;">
-                                <t t-out="object.website_id.company_id.email"/>
+                                <t t-esc="object.website_id.company_id.email"/>
                             </a>
                         </t>
                         | 
                         <a t-attf-href="#{ctx['website_http_domain']}" style=" color: #454748;">
-                            <t t-out="ctx['website_http_domain']"/>
+                            <t t-esc="ctx['website_http_domain']"/>
                         </a>
                     </td>
                 </tr>
@@ -152,10 +152,10 @@
                             <div>
                                 Hello 
                                 <t t-if="object.partner_id">
-                                    <t t-out="object.partner_id.name"/>,
+                                    <t t-esc="object.partner_id.name"/>,
                                 </t>
                                 <t t-else="">
-                                    <t t-out="object.contact_name"/>,
+                                    <t t-esc="object.contact_name"/>,
                                 </t>
                                 <br/>
                                 To control your appointments, please finish registration. To this end please follow <a t-attf-href="#{ctx['token_url']}">this link</a> and set a password up.    
@@ -184,21 +184,21 @@
                 <tbody>
                     <tr>
                         <td valign="middle" align="left">
-                            <t t-out="object.website_id.company_id.name"/>
+                            <t t-esc="object.website_id.company_id.name"/>
                         </td>
                     </tr>
                 <tr>
                     <td valign="middle" align="left" style="opacity: 0.7;">
-                        <t t-out="object.website_id.company_id.phone"/>
+                        <t t-esc="object.website_id.company_id.phone"/>
                         <t t-if="object.website_id.company_id.email">
                             | 
                             <a t-attf-href="'mailto:%s' % #{object.website_id.company_id.email}" style="color: #454748;">
-                                <t t-out="object.website_id.company_id.email"/>
+                                <t t-esc="object.website_id.company_id.email"/>
                             </a>
                         </t>
                         | 
                         <a t-attf-href="#{ctx['website_http_domain']}" style=" color: #454748;">
-                            <t t-out="ctx['website_http_domain']"/>
+                            <t t-esc="ctx['website_http_domain']"/>
                         </a>
                     </td>
                 </tr>
diff --git a/abatch1/business_appointment_website/views/templates.xml b/abatch1/business_appointment_website/views/templates.xml
index c71cde9..ae65d4c 100644
--- a/abatch1/business_appointment_website/views/templates.xml
+++ b/abatch1/business_appointment_website/views/templates.xml
@@ -897,7 +897,7 @@
                                        t-att-checked="agree_terms"
                                 />
                                 <label class="col-form-label label-optional" for="agree_terms">
-                                    <t t-out="agree_terms_text"/>                                    
+                                    <t t-esc="agree_terms_text"/>                                    
                                 </label>
                             </div>
                         </div>
diff --git a/abatch1/business_appointment_website_sale/__manifest__.py b/abatch1/business_appointment_website_sale/__manifest__.py
index 2f0aac3..ac977fd 100644
--- a/abatch1/business_appointment_website_sale/__manifest__.py
+++ b/abatch1/business_appointment_website_sale/__manifest__.py
@@ -1,7 +1,7 @@
 # -*- coding: utf-8 -*-
 {
     "name": "Universal Appointments: Website Sales",
-    "version": "15.0.1.0.1",
+    "version": "17.0.1.0.0",
     "category": "Website",
     "author": "faOtools",
     "website": "https://faotools.com/apps/15.0/universal-appointments-website-sales-614",
