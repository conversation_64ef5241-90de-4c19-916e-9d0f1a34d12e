#!/usr/bin/env bash
#
# Upgrade Factory v6.0 (Agent-Ready Architecture)
#
# This version features enhanced logging, flexible model configuration, robust
# error handling, and the architectural framework for a true "Agentic" workflow,
# including a Planning Phase and an Iterative Test & Repair Loop.
#
set -euo pipefail

#####################################################################
# CONFIGURATION & ENVIRONMENT VARIABLES
#####################################################################
# --- AI Committee Roles (Defaults can be overridden by environment variables) ---
: "${MODEL_GENERATOR:="codellama:34b"}"  # For initial code generation
: "${MODEL_CRITIC:="llama3:8b"}"        # For reviewing diffs
: "${MODEL_REFACTORER:="codellama:34b"}" # For applying feedback and fixing errors
: "${MODEL_PLANNER:="mixtral:8x7b"}"     # For architectural planning (e.g., UI migration)

# --- Colors for Logging ---
C_RESET='\033[0m'
C_RED='\033[0;31m'
C_GREEN='\033[0;32m'
C_YELLOW='\033[0;33m'
C_BLUE='\033[0;34m'
C_CYAN='\033[0;36m'
C_BOLD='\033[1m'

# --- Temporary File Locations ---
TMP_DIR="/tmp/odoo_upgrade_factory_$(date +%s)"

#####################################################################
# CORE HELPER FUNCTIONS
#####################################################################
log_info() { echo -e "${C_BLUE}INFO: $1${C_RESET}"; }
log_success() { echo -e "${C_GREEN}✅ SUCCESS: $1${C_RESET}"; }
log_warn() { echo -e "${C_YELLOW}⚠️ WARNING: $1${C_RESET}"; }
log_error() { >&2 echo -e "${C_RED}❌ ERROR: $1${C_RESET}"; }
print_header() { echo -e "\n${C_CYAN}${C_BOLD}🏭 === $1 === 🏭${C_RESET}\n"; }

cleanup() {
    log_info "Performing cleanup..."
    rm -rf "$TMP_DIR"
    # Stop docker containers if they are running
    if command -v docker-compose &> /dev/null && [ -f "docker-compose.yml" ]; then
        docker-compose down --remove-orphans >/dev/null 2>&1
    fi
    log_success "Cleanup complete."
}
# Register the cleanup function to run on script exit (successful or not)
trap cleanup EXIT

show_help() {
cat << EOF
Usage: ${0##*/} -v ODOO_VERSION -b BASE_BRANCH -d MODULE_DIR [-d MODULE_DIR_2 ...]
A fully automated Odoo upgrade factory agent.

    -v ODOO_VERSION  Required. Target Odoo version (e.g., 17.0).
    -b BASE_BRANCH   Required. The target branch for the pull request (e.g., 'upgrade-to-17.0').
    -d MODULE_DIR    Required. Path to a module directory. Use multiple times for a batch.
    -h               Show this help message.

AI models can be configured via environment variables:
    - MODEL_GENERATOR (default: "codellama:34b")
    - MODEL_CRITIC (default: "llama3:8b")
    - MODEL_REFACTORER (default: "codellama:34b")
    - MODEL_PLANNER (default: "mixtral:8x7b")
EOF
}

# AI Interaction & Parsing
# Takes a model name, a prompt file, and an output file.
run_ai_tool() {
    local model="$1"
    local prompt_file="$2"
    local output_file="$3"
    log_info "Running AI Tool: ${model}..."
    if ! ollama run "$model" < "$prompt_file" > "$output_file"; then
        log_error "The AI tool '$model' failed to run. Check if Ollama is running and the model is installed."
        exit 1
    fi
    log_success "AI Tool '$model' completed."
}

# The Python script now counts the files it creates and exits with an error if the count doesn't match.
parse_ai_code_output() {
    local output_dir="$1"
    local expected_file_count="$2"
    python -c "
import sys, os
output_dir = '$output_dir'
expected_count = int('$expected_file_count')
generated_count = 0
current_file = None
file_content = []

for line in sys.stdin:
    if line.startswith('### FILE:'):
        if current_file:
            path = os.path.join(output_dir, current_file)
            os.makedirs(os.path.dirname(path), exist_ok=True)
            with open(path, 'w', encoding='utf-8') as f:
                f.write(''.join(file_content))
            generated_count += 1
        current_file = line.split(':', 1)[1].strip().replace('\\\\', '/')
        file_content = []
    else:
        file_content.append(line)

if current_file:
    path = os.path.join(output_dir, current_file)
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, 'w', encoding='utf-8') as f:
        f.write(''.join(file_content))
    generated_count += 1

if expected_count > 0 and generated_count < expected_count:
    print(f'AI generated only {generated_count} of {expected_count} expected files.', file=sys.stderr)
    sys.exit(1)
"
}

#####################################################################
# SCRIPT INITIALIZATION & VALIDATION
#####################################################################
ODOO_VERSION=""
BASE_BRANCH=""
MODULE_DIRS=()
while getopts "hv:b:d:" opt; do
    case "$opt" in
        h) show_help; exit 0 ;;
        v) ODOO_VERSION="$OPTARG" ;;
        b) BASE_BRANCH="$OPTARG" ;;
        d) MODULE_DIRS+=("$OPTARG") ;;
        *) show_help; exit 1 ;;
    esac
done

# --- Pre-flight Checks ---
if [ -z "$ODOO_VERSION" ] || [ -z "$BASE_BRANCH" ] || [ ${#MODULE_DIRS[@]} -eq 0 ]; then
    log_error "Odoo version (-v), base branch (-b), and at least one module (-d) are required."
    show_help
    exit 1
fi
for cmd in gh docker-compose git ollama; do
    if ! command -v "$cmd" &>/dev/null; then
        log_error "'$cmd' is not installed. Please install it to continue."
        exit 1
    fi
done
if ! gh auth status &>/dev/null; then log_error "Not logged into GitHub CLI. Please run 'gh auth login'."; exit 1; fi
if ! [ -f "docker-compose.yml" ]; then log_error "No 'docker-compose.yml' found in the current directory."; exit 1; fi
if [[ -n $(git status --porcelain) ]]; then log_error "Working directory is not clean. Please commit or stash changes."; exit 1; fi

# --- Dynamic variables ---
BATCH_NAME=$(basename "${MODULE_DIRS[0]}")
[[ ${#MODULE_DIRS[@]} -gt 1 ]] && BATCH_NAME+="-batch"
BRANCH_NAME="feat/upgrade-v${ODOO_VERSION}-${BATCH_NAME}-$(date +%s)"
DB_NAME="testdb-$(echo "$ODOO_VERSION" | tr '.' '-')-$(date +%s)"

# --- Final Confirmation ---
echo -e "${C_YELLOW}Upgrade Factory v6.0 Initializing...${C_RESET}"
echo "------------------------------------------------"
echo -e "Odoo Version:  ${C_BOLD}$ODOO_VERSION${C_RESET}"
echo -e "Base Branch:   ${C_BOLD}$BASE_BRANCH${C_RESET}"
echo -e "New Branch:    ${C_BOLD}$BRANCH_NAME${C_RESET}"
echo -e "Modules:       ${C_BOLD}$(printf "'%s' " "${MODULE_DIRS[@]}")${C_RESET}"
echo "------------------------------------------------"
read -p "Proceed with the upgrade? (y/N) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_warn "Upgrade cancelled by user."
    exit 0
fi

#####################################################################
#                     THE AGENTIC PIPELINE (v6.0)
#####################################################################

# STEP 1: PREPARE ENVIRONMENT
print_header "STEP 1: PREPARING ENVIRONMENT"
mkdir -p "$TMP_DIR"/{original,generated_v1,generated_final,prompts,logs}
LOG_FILE="$TMP_DIR/logs/test_results.log"
PR_BODY_FILE="$TMP_DIR/pr_body.md"

log_info "Creating new branch: $BRANCH_NAME"
git checkout "$BASE_BRANCH"
if ! git rev-parse --abbrev-ref --symbolic-full-name @{u} &>/dev/null; then
    log_error "Local branch '$BASE_BRANCH' is not tracking a remote branch. Please run 'git push -u origin $BASE_BRANCH'."
    exit 1
fi
git pull
git checkout -b "$BRANCH_NAME"

# Collect all files to be processed into an array
ALL_FILES=()
for module_path in "${MODULE_DIRS[@]}"; do
    while IFS= read -r file; do
        ALL_FILES+=("$file")
    done < <(find "$module_path" -type f \( -name "*.py" -o -name "*.xml" -o -name "*.js" -o -name "*.scss" -o -name "*.css" \))
done
SOURCE_FILE_COUNT=${#ALL_FILES[@]}
log_info "Found $SOURCE_FILE_COUNT files to process."

# STEP 2: AGENTIC PLANNING PHASE (Future Implementation)
print_header "STEP 2: ARCHITECTURAL PLANNING"
log_info "This step would use MODEL_PLANNER to analyze UI files."
log_info "It would generate a JSON plan for migrating legacy JS to Owl 2."
log_info "For now, we will proceed with a direct upgrade of all files."
# PLACEHOLDER:
# PROMPT_PLANNER="$TMP_DIR/prompts/prompt_planner.txt"
# MIGRATION_PLAN_JSON="$TMP_DIR/migration_plan.json"
# create_planner_prompt "$PROMPT_PLANNER" "${ALL_FILES[@]}"
# run_ai_tool "$MODEL_PLANNER" "$PROMPT_PLANNER" "$MIGRATION_PLAN_JSON"
# log_success "Migration plan created."


# STEP 3: INITIAL CODE GENERATION
print_header "STEP 3: INITIAL CODE GENERATION"
# --- GENERATOR ---
PROMPT_GENERATOR="$TMP_DIR/prompts/prompt_generator.txt"
log_info "Creating generator prompt..."
cat > "$PROMPT_GENERATOR" << EOF
You are an expert Odoo full-stack migration engineer. Your task is to generate the complete, upgraded source code for the following files to make them compatible with Odoo version ${ODOO_VERSION}.
This includes updating Python code for API changes, converting legacy XML views to modern syntax, and rewriting legacy JavaScript widgets into modern Owl 2 components.
For each file, you MUST respond with a file path marker line '### FILE: [path]' followed by the complete, raw source code. Do not add any other explanations.

Begin now. Files to upgrade:
EOF
for f in "${ALL_FILES[@]}"; do
    relative_path=$(git ls-files --full-name "$f")
    echo -e "\n### FILE: $relative_path" >> "$PROMPT_GENERATOR"
    cat "$f" >> "$PROMPT_GENERATOR"
done

ollama run "$MODEL_GENERATOR" < "$PROMPT_GENERATOR" | parse_ai_code_output "$TMP_DIR/generated_v1" "$SOURCE_FILE_COUNT"
log_success "First draft generated in $TMP_DIR/generated_v1"

log_info "Copying original files for diff..."
for module_path in "${MODULE_DIRS[@]}"; do
    rsync -a --prune-empty-dirs --include="*/" --include="*.*" --exclude="*" "$module_path/" "$TMP_DIR/original/$module_path/"
    for f in "${ALL_FILES[@]}"; do
        if [[ "$f" == "$module_path"* ]]; then
            mkdir -p "$TMP_DIR/original/$(dirname "$f")"
            cp "$f" "$TMP_DIR/original/$f"
        fi
    done
done
diff -ruN "$TMP_DIR/original/" "$TMP_DIR/generated_v1/" > "${TMP_DIR}/upgrade_v1.diff" || true
FIRST_DRAFT_DIFF="${TMP_DIR}/upgrade_v1.diff"

# --- CRITIC ---
PROMPT_CRITIC="$TMP_DIR/prompts/prompt_critic.txt"
log_info "Creating critic prompt..."
cat > "$PROMPT_CRITIC" << EOF
You are a senior QA engineer reviewing a proposed code change for an Odoo ${ODOO_VERSION} upgrade.
Your task is to find flaws. Be concise. Provide feedback as a bulleted list. If you find no issues, state "No issues found."

Diff to review:
\`\`\`diff
$(cat "$FIRST_DRAFT_DIFF")
\`\`\`
EOF
run_ai_tool "$MODEL_CRITIC" "$PROMPT_CRITIC" "$TMP_DIR/critic_review.txt"
log_info "Critic's review:"
cat "$TMP_DIR/critic_review.txt"

# --- REFACTORER ---
PROMPT_REFACTORER="$TMP_DIR/prompts/prompt_refactorer.txt"
log_info "Creating refactorer prompt..."
cat > "$PROMPT_REFACTORER" << EOF
You are an expert Odoo refactoring engineer. Produce the perfect, final source code based on a reviewer's feedback.
Generate the complete, final code for each file, addressing all points in the feedback.
IMPORTANT: For each file, respond with a file path marker line '### FILE: [path]' followed by the complete, raw source code. Do not add explanations.

Reviewer's Feedback:
$(cat "$TMP_DIR/critic_review.txt")

Now, generate the final, corrected code for the following original files.
Files:
EOF
for f in "${ALL_FILES[@]}"; do
    relative_path=$(git ls-files --full-name "$f")
    echo -e "\n### FILE: $relative_path" >> "$PROMPT_REFACTORER"
    cat "$f" >> "$PROMPT_REFACTORER"
done
ollama run "$MODEL_REFACTORER" < "$PROMPT_REFACTORER" | parse_ai_code_output "$TMP_DIR/generated_final" "$SOURCE_FILE_COUNT"
log_success "Final version generated in $TMP_DIR/generated_final"

diff -ruN "$TMP_DIR/original/" "$TMP_DIR/generated_final/" > "$FINAL_DRAFT_DIFF" || true
log_success "Final diff generated at $FINAL_DRAFT_DIFF"

# STEP 4: APPLY & COMMIT
print_header "STEP 4: VALIDATING & APPLYING REFACTORED CODE"
if ! git apply --check "$FINAL_DRAFT_DIFF"; then
    log_error "The final AI-generated diff is invalid. Aborting pipeline."
    exit 1
fi
patch -p1 < "$FINAL_DRAFT_DIFF"
git add .
git commit -m "feat(upgrade): AI-driven upgrade to v${ODOO_VERSION} for batch ${BATCH_NAME}"
log_success "Changes committed to branch $BRANCH_NAME."

# STEP 5: ITERATIVE TEST & REPAIR LOOP (Future Implementation)
print_header "STEP 5: DOCKERIZED TEST SUITE"
MOD_LIST=$(for d in "${MODULE_DIRS[@]}"; do basename "$d"; done | tr '\n' ',' | sed 's/,$//')
export ODOO_IMAGE_TAG="${ODOO_VERSION}"
log_info "🐳 Starting Dockerized Odoo ${ODOO_VERSION} and Postgres..."
docker-compose up -d db
log_info "⏳ Waiting for PostgreSQL to be ready..."
until docker-compose exec db pg_isready -U odoo -q &>/dev/null; do
  sleep 2
done
log_success "PostgreSQL is ready."
log_info "🧪 Running tests for modules: $MOD_LIST. Log: $LOG_FILE"
set +e
docker-compose run --rm -e ODOO_RC="--db_user=odoo --db_password=odoo --db_host=db --db_port=5432" odoo \
  --database="$DB_NAME" --init="$MOD_LIST" --test-enable --stop-after-init --log-level=test > "$LOG_FILE" 2>&1
TEST_EXIT_CODE=${PIPESTATUS[0]}
set -e
log_info "🛑 Stopping Docker containers..."
docker-compose down --remove-orphans

# --- AGENTIC REPAIR LOOP PLACEHOLDER ---
# This is where the true agentic behavior would live.
# if [ "$TEST_EXIT_CODE" -ne 0 ]; then
#     log_warn "Initial tests failed. Entering agentic repair loop..."
#     # 1. Parse LOG_FILE to extract the Python traceback and failing file.
#     # 2. Create a new, highly-targeted prompt for MODEL_REFACTORER.
#     #    e.g., "Fix this specific traceback in this file."
#     # 3. Call the AI to generate a patch for the single failing file.
#     # 4. Apply the patch.
#     # 5. Loop back to run the tests again, up to a max retry count.
# fi

# STEP 6: REPORTING & GITHUB AUTOMATION
print_header "STEP 6: REPORTING & GITHUB AUTOMATION"
if [ "$TEST_EXIT_CODE" -ne 0 ]; then
    log_error "Tests FAILED. The pipeline has stopped. See log for details: $LOG_FILE"
    log_warn "The failed changes are isolated in branch: $BRANCH_NAME"
    log_warn "Inspect and debug them before cleaning up with: git checkout $BASE_BRANCH && git branch -D $BRANCH_NAME"
    exit 1
else
    log_success "All tests passed successfully!"
    log_info "📝 Generating documentation for Pull Request..."
    PR_TITLE="feat(upgrade): Automated Odoo ${ODOO_VERSION} upgrade for batch: ${BATCH_NAME}"
    cat > "$PR_BODY_FILE" << EOF
### Odoo ${ODOO_VERSION} Automated Upgrade Summary
This Pull Request was automatically generated by the Upgrade Factory pipeline on $(date).

**Modules in this Batch:**
$(for d in "${MODULE_dirs[@]}"; do echo "- \`$(basename "$d")\`"; done)
---
**AI Critic Review Summary:**
A senior reviewer AI analyzed the first draft and provided the following feedback, which was addressed in this final version:
\`\`\`
$(cat "$TMP_DIR/critic_review.txt")
\`\`\`
---
**Test Results:**
- ✅ All automated tests passed successfully in a clean Docker environment. A full log was generated during the run.
EOF
    log_info "🚀 Pushing branch and creating Pull Request on GitHub..."
    git push origin "$BRANCH_NAME"
    gh pr create --title "$PR_TITLE" --body-file "$PR_BODY_FILE" --base "$BASE_BRANCH"
    log_success "🎉 Success! Your automated Pull Request has been created."
    git checkout "$BASE_BRANCH"
fi