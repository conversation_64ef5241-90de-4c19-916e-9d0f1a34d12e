<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="module_category_business_appointment" model="ir.module.category">
        <field name="name">Business Appointments</field>
        <field name="sequence">20</field>
    </record>
    <record id="group_ba_user" model="res.groups">
        <field name="name">Appointments: Only Own</field>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="category_id" ref="business_appointment.module_category_business_appointment"/>
    </record>
    <record id="group_ba_admin" model="res.groups">
        <field name="name">Appointments: Administrator</field>
        <field name="implied_ids" eval="[(4, ref('business_appointment.group_ba_user'))]"/>
        <field name="category_id" ref="business_appointment.module_category_business_appointment"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>
    <record id="group_business_appointment_rating" model="res.groups">
        <field name="name">Rating for Business Appointments</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <data noupdate="0">
        <!-- Business Resource Types-->
        <record id="business_resource_type_user" model="ir.rule">
            <field name="name">Appointments Users - Business.Resource.Type - Read Own</field>
            <field name="model_id" ref="business_appointment.model_business_resource_type"/>
            <field name="domain_force">[
                '|',
                    ('company_id','=', False),
                    ('company_id', 'in', company_ids),
            ]</field>
            <field name="groups" eval="[(4, ref('business_appointment.group_ba_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        <!-- Business Resources -->
        <record id="business_resource_user" model="ir.rule">
            <field name="name">Appointments Users - Business.Resource - Read Own</field>
            <field name="model_id" ref="business_appointment.model_business_resource"/>
            <field name="domain_force">[
                '&amp;',
                    '|', '|',
                        ('user_id', '=', user.id),
                        ('user_id', '=', False),
                        ('message_partner_ids', 'in', [user.partner_id.id]),
                    '|',
                        ('company_id','=', user.company_id.id),
                        ('company_id','=', False),
            ]</field>
            <field name="groups" eval="[(4, ref('business_appointment.group_ba_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        <record id="business_resource_admin" model="ir.rule">
            <field name="name">Appointments Admins - Business.Resource - Full Rights</field>
            <field name="model_id" ref="business_appointment.model_business_resource"/>
            <field name="domain_force">[
                '|',
                    ('company_id','=', False),
                    ('company_id', 'in', company_ids),
            ]</field>
            <field name="groups" eval="[(4, ref('business_appointment.group_ba_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        <!-- Pre-reservations -->
        <record id="business_appointment_core_user" model="ir.rule">
            <field name="name">Appointments Users - Reservations - Update Own</field>
            <field name="model_id" ref="business_appointment.model_business_appointment_core"/>
            <field name="domain_force">[
                '|',
                    ('user_id', '=', user.id),
                    ('user_id', '=', False),
                '|',
                    ('company_id','=', False),
                    ('company_id', 'in', company_ids),
            ]</field>
            <field name="groups" eval="[(4, ref('business_appointment.group_ba_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        <record id="business_appointment_core_admin" model="ir.rule">
            <field name="name">Appointments Admins - Reservations - Full Rights</field>
            <field name="model_id" ref="business_appointment.model_business_appointment_core"/>
            <field name="domain_force">[
                '|',
                    ('company_id','=', False),
                    ('company_id', 'in', company_ids),
            ]</field>
            <field name="groups" eval="[(4, ref('business_appointment.group_ba_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        <!-- Appointments -->
        <record id="business_appointment_followers" model="ir.rule">
            <field name="name">Appointments Followers - Appointments - Read Followed</field>
            <field name="model_id" ref="business_appointment.model_business_appointment"/>
            <field name="domain_force">[
                '&amp;',
                    '|', '|', '|',
                        ('user_id', '=', user.id),
                        ('user_id', '=', False),
                        ('message_partner_ids', 'in', [user.partner_id.id]),
                        ('resource_id.message_partner_ids', 'in', [user.partner_id.id]),
                    '|',
                        ('company_id','=', user.company_id.id),
                        ('company_id','=', False),
            ]</field>
            <field name="groups" eval="[(4, ref('business_appointment.group_ba_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        <record id="business_appointment_user" model="ir.rule">
            <field name="name">Appointments Users - Appointments - Update Own</field>
            <field name="model_id" ref="business_appointment.model_business_appointment"/>
            <field name="domain_force">[
                '|',
                    ('user_id', '=', user.id),
                    ('user_id', '=', False),
                '|',
                    ('company_id','=', False),
                    ('company_id', 'in', company_ids),
            ]</field>
            <field name="groups" eval="[(4, ref('business_appointment.group_ba_user'))]"/>
            <field name="perm_read" eval="False"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        <record id="business_appointment_admin" model="ir.rule">
            <field name="name">Appointments Admins - Appointments - Full Rights</field>
            <field name="model_id" ref="business_appointment.model_business_appointment"/>
            <field name="domain_force">[
                '|',
                    ('company_id','=', False),
                    ('company_id', 'in', company_ids),
            ]</field>
            <field name="groups" eval="[(4, ref('business_appointment.group_ba_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        <record id="business_appointment_sms_template_user" model="ir.rule">
            <field name="name">SMS Template: Appointments</field>
            <field name="model_id" ref="sms.model_sms_template"/>
            <field name="groups" eval="[(4, ref('business_appointment.group_ba_user'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>
    </data>

</odoo>
