/*                      Backend Helper
 **********************************************************/
@mixin o_appointment_type_backend_helper {
    .o_view_nocontent {
        top: 10vh;
        .o_view_no_content_appointment_type:before {
            @extend %o-nocontent-init-image;
            background: transparent url(/appointment/static/src/img/appointment-type-helper.svg) no-repeat center;
            background-size: contain;
            width: 100%;
            height: 325px;
        }
    }
}

/*                      List View
 **********************************************************/
 .o_appointment_type_view_list {
    @include o_appointment_type_backend_helper;
 }

/*                      Kanban view
 **********************************************************/
.o_kanban_dashboard.o_appointment_kanban .o_kanban_renderer {
    @include o_appointment_type_backend_helper;
    &.o_kanban_grouped {
        .o_appointment_kanban_card_ungrouped {
            display:none !important;
        }
        .o_kanban_group:not(.o_column_folded){
            @include media-breakpoint-up(sm) {
                min-width: 380px;
            }
        }
    }

    .appointment_type_action_toggler {
        --KanbanRecord-padding-v: 12px;
        --KanbanRecord-padding-h: 6px;
    }

    &.o_kanban_ungrouped {
        // ungrouped layout as list re-used form survey_survey
        .o_appointment_kanban_card_grouped {
            display:none !important;
        }
        padding: 0;

        .o_kanban_record {
            width: 100%;
            margin: 0px;
            .appointment_type_action_toggler {
                @include media-breakpoint-up(sm) {
                    right: 7% !important;
                    top: 30%;
                    .appointment_type_action_toggler_text {
                        display: inline-block !important;
                    }
                }
                .text-large {
                    line-height: 1.1em;
                    font-size: 120%;
                    font-weight: 300;
                }
            }
            .o_appointment_kanban_card {
                margin: 0;
                border-top: 0 !important;
            }
        }
        .o_m2m_avatar, .o_m2m_avatar_empty {
            width: 32px;
            height: 32px;
        }
        .o_m2m_avatar_empty {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: var(--Tag-font-size, #{$font-size-sm});
        }
    }
    // Ungrouped specific
    .o_appointment_kanban_card_ungrouped {
        @include media-breakpoint-up(sm) {
            margin-right: 8rem;
        }
        min-height: 4rem;
        .ribbon {
            --Ribbon-wrapper-height: 100%;
        }
        .text-large {
            line-height: 1.1em;
            font-size: 120%;
            font-weight: 300;
        }
    }

    // Grouped specific
    .o_appointment_kanban_card_grouped {
        .o_kanban_record {
            min-height: 165px;
         }
    }

    .o_appointment_kanban_boxes {
        flex-flow: row nowrap;

        .o_appointment_kanban_box {
            flex: 1 1 auto;
            flex-flow: row nowrap;
            &:first-child {
                justify-content: flex-start;
                padding-left: 16px;
            }
            div:last-child {
                justify-content: flex-end;
                text-align: right;
            }

        }
    }
}

/*                      Form View
 **********************************************************/
.o_appointment_slots_list {
    th.o_list_button {
        width: 2.4em!important;
    }

    button.fa-long-arrow-right {
        pointer-events: none;
    }
}
