<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="appointment_default_resource_calendar" model="resource.calendar">
            <field name="name">Appointment Resource Default Calendar</field>
            <field name="company_id" eval="False"/>
            <field name="hours_per_day">24</field>
            <field name="attendance_ids"
                eval="[(5, 0, 0),
                    (0, 0, {'name': 'Monday Morning', 'dayofweek': '0', 'hour_from': 0, 'hour_to': 11.99, 'day_period': 'morning'}),
                    (0, 0, {'name': 'Monday Afternoon', 'dayofweek': '0', 'hour_from': 12, 'hour_to': 24, 'day_period': 'afternoon'}),
                    (0, 0, {'name': 'Tuesday Morning', 'dayofweek': '1', 'hour_from': 0, 'hour_to': 11.99, 'day_period': 'morning'}),
                    (0, 0, {'name': 'Tuesday Afternoon', 'dayofweek': '1', 'hour_from': 12, 'hour_to': 24, 'day_period': 'afternoon'}),
                    (0, 0, {'name': 'Wednesday Morning', 'dayofweek': '2', 'hour_from': 0, 'hour_to': 11.99, 'day_period': 'morning'}),
                    (0, 0, {'name': 'Wednesday Afternoon', 'dayofweek': '2', 'hour_from': 12, 'hour_to': 24, 'day_period': 'afternoon'}),
                    (0, 0, {'name': 'Thursday Morning', 'dayofweek': '3', 'hour_from': 0, 'hour_to': 11.99, 'day_period': 'morning'}),
                    (0, 0, {'name': 'Thursday Afternoon', 'dayofweek': '3', 'hour_from': 12, 'hour_to': 24, 'day_period': 'afternoon'}),
                    (0, 0, {'name': 'Friday Morning', 'dayofweek': '4', 'hour_from': 0, 'hour_to': 11.99, 'day_period': 'morning'}),
                    (0, 0, {'name': 'Friday Afternoon', 'dayofweek': '4', 'hour_from': 12, 'hour_to': 24, 'day_period': 'afternoon'}),
                    (0, 0, {'name': 'Saturday Morning', 'dayofweek': '5', 'hour_from': 0, 'hour_to': 11.99, 'day_period': 'morning'}),
                    (0, 0, {'name': 'Saturday Afternoon', 'dayofweek': '5', 'hour_from': 12, 'hour_to': 24, 'day_period': 'afternoon'}),
                    (0, 0, {'name': 'Sunday Morning', 'dayofweek': '6', 'hour_from': 0, 'hour_to': 11.99, 'day_period': 'morning'}),
                    (0, 0, {'name': 'Sunday Afternoon', 'dayofweek': '6', 'hour_from': 12, 'hour_to': 24, 'day_period': 'afternoon'}),
                ]"
            />
        </record>
    </data>
</odoo>
