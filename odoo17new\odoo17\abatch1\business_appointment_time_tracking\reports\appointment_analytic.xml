<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="appointment_analytic_view_search" model="ir.ui.view">
        <field name="name">appointment.analytic.search.time.tracking</field>
        <field name="model">appointment.analytic</field>
        <field name="inherit_id" ref="business_appointment.appointment_analytic_view_search"/>
        <field name="arch" type="xml">
            <filter name="my_appointments" position="after">
                <separator/>
                <filter string="No Zero Tracked Duration"
                        name="tracked_duration"
                        domain="[('total_real_duration', '!=', 0)]"
                />    
            </filter>
        </field>
    </record>

</odoo>
