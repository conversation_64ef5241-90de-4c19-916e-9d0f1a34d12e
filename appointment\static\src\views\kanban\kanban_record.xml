<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="appointment.AppointmentTypeKanbanRecordMenu"
       t-inherit="web.KanbanRecordMenu" t-inherit-mode="primary">
        <xpath expr="//Dropdown" position="attributes">
            <attribute name="class" add="' appointment_type_action_toggler'" separator="+"/>
            <attribute name="menuClass" add="' min-w-0'" separator="+"/>
        </xpath>
        <xpath expr="//span[hasclass('fa-ellipsis-v')]" position="after">
            <span class="text-large appointment_type_action_toggler_text d-none">Action</span>
        </xpath>
        <xpath expr="//span[hasclass('fa-ellipsis-v')]" position="replace">
            <span class="fa fa-cog mx-1 d-none d-sm-inline-block"/>
            <span class="fa fa-ellipsis-v m-2 d-sm-none"/>
        </xpath>
    </t>
</templates>
