<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_advanced_appointment_config_settings" model="ir.ui.view">
        <field name="name">advanced.appointment.res.config.settings.view.form</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="99"/>
        <field name="arch" type="xml">
            <form class="oe_form_configuration">
                <header>
                    <button string="Apply" type="object" name="execute" class="oe_highlight"/>
                    <button string="Cancel" type="object" name="cancel" class="oe_link" special="cancel"/>
                </header>
                <sheet>
                    <div class="app_settings_block" data-string="Advanced Appointments" string="Advanced Appointments" data-key="advanced_appointment_manager">
                        <h2>Advanced Appointment Policy</h2>
                        <div class="row mt16 o_settings_container">
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_right_pane">
                                    <label for="lateness_policy"/>
                                    <div class="text-muted">
                                        Set the automated policy for handling late clients.
                                    </div>
                                    <div class="mt8">
                                        <field name="lateness_policy" class="o_light_label"/>
                                    </div>
                                    <div class="content-group mt16" invisible="lateness_policy == 'none'">
                                        <div class="row" invisible="lateness_policy != 'no_show'">
                                            <label for="no_show_delay_minutes" class="col-lg-5 o_light_label"/>
                                            <field name="no_show_delay_minutes" class="oe_inline"/> minutes after start
                                        </div>
                                        <div class="row" invisible="lateness_policy != 'forfeit'">
                                            <label for="forfeit_minutes_before" class="col-lg-5 o_light_label"/>
                                            <field name="forfeit_minutes_before" class="oe_inline"/> minutes before start
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_advanced_appointment_config_settings" model="ir.actions.act_window">
        <field name="name">Advanced Appointments</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="view_id" ref="view_advanced_appointment_config_settings"/>
        <field name="context">{'module': 'advanced_appointment_manager'}</field>
    </record>

    <menuitem
        id="menu_appointment_settings"
        name="Advanced Appointments"
        parent="base.menu_administration"
        sequence="50"
        action="action_advanced_appointment_config_settings"
        groups="base.group_system"/>
</odoo>