<?xml version="1.0"?>
<odoo>

    <record  id="business_resource_view_form" model="ir.ui.view" >
        <field name="name">business.resource.form.website</field>
        <field name="model">business.resource</field>
        <field name="inherit_id" ref="business_appointment.business_resource_view_form"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button class="oe_stat_button" 
                        name="action_portal_publish_button"
                        type="object" 
                        icon="fa-globe" 
                >
                    <field name="is_published" widget="website_publish_button"/>
                </button>
            </div>
            <notebook position="inside">
                <page string="Website">
                    <group>
                        <field name="website_id"
                               options="{'no_create_edit': 1,'no_quick_create': 1,}"
                               groups="website.group_multi_website"
                        />
                        <field name="donotshow_full_description"/>
                    </group>
                    <button name="edit_website"
                            type="object"
                            string="edit full website description"
                            class="oe_link"
                            icon="fa-arrow-right"
                    />
                </page>
            </notebook>
        </field>
    </record>

    <record id="business_resource_publish" model="ir.actions.server">
        <field name="name">Website Publish</field>
        <field name="type">ir.actions.server</field>
        <field name="model_id" ref="business_appointment.model_business_resource"/>
        <field name="binding_model_id" ref="business_appointment.model_business_resource"/>
        <field name="state">code</field>
        <field name="code">
if records:
    records.write({"website_published": True})
        </field>
    </record>
    <record id="business_resource_ubpublish" model="ir.actions.server">
        <field name="name">Website Unpublish</field>
        <field name="type">ir.actions.server</field>
        <field name="model_id" ref="business_appointment.model_business_resource"/>
        <field name="binding_model_id" ref="business_appointment.model_business_resource"/>
        <field name="state">code</field>
        <field name="code">
if records:
    records.write({"website_published": False})
        </field>
    </record>

</odoo>
