# -*- coding: utf-8 -*-
from odoo import fields, models

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    lateness_policy = fields.Selection(
        related='company_id.lateness_policy',
        readonly=False,
    )
    no_show_delay_minutes = fields.Integer(
        related='company_id.no_show_delay_minutes',
        readonly=False,
    )
    forfeit_minutes_before = fields.Integer(
        related='company_id.forfeit_minutes_before',
        readonly=False,
    )