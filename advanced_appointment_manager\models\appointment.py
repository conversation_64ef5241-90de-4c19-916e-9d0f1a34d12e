# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import timedelta
import logging

_logger = logging.getLogger(__name__)

class WebsiteAppointmentAppointment(models.Model):
    _inherit = 'calendar.event'

    approval_state = fields.Selection([
        ("pending", "Pending"),
        ("approved", "Approved"),
        ("rejected", "Rejected"),
        ("cancelled", "Cancelled"),
    ], string="Approval Status", default="pending", copy=False, tracking=True)

    checkin_state = fields.Selection([
        ("waiting", "Waiting for Client"),
        ("in_progress", "In Progress"),
        ("completed", "Completed"),
        ("no_show", "No Show"),
        ("forfeited", "Forfeited"),
    ], string="Check-in Status", default="waiting", copy=False, tracking=True)

    def _grant_portal_access(self, partner):
        if not partner or not partner.email:
            return True

        email_login = partner.email.lower()
        
        if self.env['res.users'].with_context(active_test=False).search_count([('login', '=', email_login)]) > 0:
            _logger.info(f"User with login '{email_login}' already exists. Skipping portal access grant.")
            return True

        portal_group = self.env.ref('base.group_portal', raise_if_not_found=False)
        if not portal_group:
            raise UserError(_("The 'Portal' security group is missing."))

        try:
            new_user = self.env['res.users'].create({
                'name': partner.name or partner.email,
                'login': email_login,
                'partner_id': partner.id,
                'company_id': self.env.company.id,
                'groups_id': [(6, 0, [portal_group.id])]
            })
            _logger.info(f"Created new portal user {new_user.login} for partner {partner.name}")
        except Exception as e:
            _logger.error(f"Could not create portal user for {partner.name} (ID: {partner.id}): {e}")

        return True

    def action_approve(self):
        for appt in self:
            if appt.approval_state != 'pending': continue
            appt.write({'approval_state': 'approved', 'checkin_state': 'waiting'})
            self._grant_portal_access(appt.partner_id)
            template = self.env.ref('advanced_appointment_manager.email_template_appointment_approved_aam', raise_if_not_found=False)
            if template:
                # Changed to False to add email to queue for inspection.
                template.send_mail(appt.id, force_send=False)
        return True

    def action_reject(self):
        for appt in self:
            if appt.approval_state != 'pending': continue
            appt.approval_state = 'rejected'
            template = self.env.ref('advanced_appointment_manager.email_template_appointment_rejected_aam', raise_if_not_found=False)
            if template:
                # Changed to False to add email to queue for inspection.
                template.send_mail(appt.id, force_send=False)
        return True

    def action_check_in(self):
        self.ensure_one()
        self.checkin_state = "in_progress"

    def action_mark_completed(self):
        self.ensure_one()
        self.checkin_state = "completed"

    def action_mark_no_show(self):
        self.ensure_one()
        self.checkin_state = "no_show"

    def action_mark_forfeited(self):
        self.ensure_one()
        if self.checkin_state == 'forfeited': return
        self.checkin_state = 'forfeited'
        template = self.env.ref('advanced_appointment_manager.email_template_appointment_forfeited_aam', raise_if_not_found=False)
        if template:
            # Changed to False to add email to queue for inspection.
            template.send_mail(self.id, force_send=False)

    def action_cancel(self):
        if not self.filtered(lambda appt: appt.approval_state in ['pending', 'approved']): return False
        return self.write({'approval_state': 'cancelled'})

    @api.model
    def _cron_update_appointment_status(self):
        company = self.env.company
        lateness_policy = company.lateness_policy
        if lateness_policy == 'none': return
        now = fields.Datetime.now()
        search_window_past = now - timedelta(days=2)
        search_window_future = now + timedelta(days=2)
        waiting_appointments = self.search([
            ('start', '>=', search_window_past), ('start', '<=', search_window_future),
            ('approval_state', '=', 'approved'), ('checkin_state', '=', 'waiting')
        ])
        if not waiting_appointments: return
        if lateness_policy == 'no_show':
            no_show_delay_minutes = company.no_show_delay_minutes
            for appt in waiting_appointments:
                if appt.start < now - timedelta(minutes=no_show_delay_minutes):
                    _logger.info(f"Marking appointment {appt.id} as No Show based on policy.")
                    appt.action_mark_no_show()
        elif lateness_policy == 'forfeit':
            forfeit_minutes_before = company.forfeit_minutes_before
            for appt in waiting_appointments:
                deadline = appt.start - timedelta(minutes=forfeit_minutes_before)
                if deadline <= now < appt.start:
                    _logger.info(f"Marking appointment {appt.id} as Forfeited based on policy.")
                    appt.action_mark_forfeited()