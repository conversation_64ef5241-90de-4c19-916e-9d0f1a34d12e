<?xml version="1.0"?>
<odoo>

    <record id="business_appointment_view_search" model="ir.ui.view">
        <field name="name">business.appointment.search.sale</field>
        <field name="model">business.appointment</field>
        <field name="inherit_id" ref="business_appointment.business_appointment_view_search"/>        
        <field name="arch" type="xml">
            <field name="resource_type_id" position="after">
                <field name="order_id"/>
            </field>
        </field>
    </record>

    <record id="business_appointment_view_form" model="ir.ui.view">
        <field name="name">business.appointment.form.sale</field>
        <field name="model">business.appointment</field>
        <field name="inherit_id" ref="business_appointment.business_appointment_view_form"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button name="action_create_sale_order"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-money"
                        attrs="{'invisible': [('order_id', '!=', False)]}"
                >
                    To Sale
                </button>
                <button name="action_adapt_sale_order"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-money"
                        attrs="{'invisible': ['|', ('order_id', '=', False), ('sale_state', 'not in', ['draft', 'sent'])]}"
                >
                    Adapt Sale
                </button>
            </div>
            <field name="pricelist_id" position="before">
                <field name="order_id" readonly="1" />
                <field name="sale_state" invisible="1"/>
            </field>
        </field>
    </record>

</odoo>
