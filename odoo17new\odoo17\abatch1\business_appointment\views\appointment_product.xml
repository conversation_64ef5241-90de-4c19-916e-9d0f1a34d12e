<?xml version="1.0"?>
<odoo>

    <record id="appointment_product_view_search" model="ir.ui.view">
        <field name="name">appointment.product.search</field>
        <field name="model">appointment.product</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="product_id"/>
                <field name="ba_description"/>
                <filter string="Activities Todo" name="activities_my" domain="[('activity_ids.user_id', '=', uid)]"/>
                <separator/>
                <filter string="Late Activities"
                        name="activities_overdue"
                        domain="[('activity_ids.date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                />
                <filter string="Today Activities"
                        name="activities_today"
                        domain="[('activity_ids.date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"
                />
                <filter string="Future Activities"
                        name="activities_upcoming_all"
                        domain="[('activity_ids.date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"
                />
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>
    <record id="appointment_product_view_form" model="ir.ui.view">
        <field name="name">appointment.product.form.appointment</field>
        <field name="model">appointment.product</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(business_appointment.rating_rating_action_appointment_product)d" 
                                type="action" 
                                attrs="{'invisible': [('rating_satisfaction', '=', -1)]}" 
                                class="oe_stat_button oe_percent" 
                                icon="fa-smile-o" 
                                groups="business_appointment.group_business_appointment_rating"
                        >
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">
                                    <field name="rating_satisfaction" nolabel="1"/>
                                </span>
                                <span class="o_stat_text">%</span>
                            </div>
                        </button>
                    </div>
                    <field name="image_1920"
                           widget="image"
                           class="oe_avatar oe_left"
                           options="{'preview_image': 'image_128'}"
                    />
                    <div class="oe_title">
                        <h1>
                            <field name="name"
                                   placeholder="resource reference"
                                   class="oe_inline"
                                   required="1"
                            />
                        </h1>
                    </div>            
                    <group name="ba_ap_main_details">
                        <group>
                            <label for="appointment_duration"/>
                            <div>
                                <field name="appointment_duration"
                                       widget="float_time"
                                       attrs="{'invisible': [('duration_uom', '!=', 'hours')]}"
                                       class="oe_inline"
                                />
                                <field name="appointment_duration_days"
                                       attrs="{'invisible': [('duration_uom', '!=', 'days')]}"
                                       class="oe_inline"
                                />
                                <field name="duration_uom" class="oe_inline"/>
                                <field name="active" invisible="1" />
                            </div>
                            <field name="location"/>
                        </group>
                        <group>
                            <field name="product_id" options="{'no_create_edit': 1, 'no_quick_create': 1}" />
                            <field name="suggested_product_ids"
                                   widget="many2many_tags"
                                   options="{'no_quick_create': 1, 'no_create_edit': 1}"
                            />
                        </group>
                    </group>                        
                    <notebook>
                        <page string="Settings" name="settings">
                            <group name="overall_appointments_configs">
                                <group name="general_appointments_configs">
                                    <field name="start_round_rule"
                                           attrs="{'invisible': [('duration_uom', '!=', 'hours')]}"
                                           widget="float_time"
                                    />
                                    <field name="start_round_rule_days"
                                           attrs="{'invisible': [('duration_uom', '!=', 'days')]}"
                                           widget="float_time"
                                    />
                                    <field name="company_id"
                                           options="{'no_create_edit': 1, 'no_quick_create': 1}"
                                           groups="base.group_multi_company"
                                    />
                                    <field name="extra_working_calendar_id"/>
                                </group>
                                <group name="manual_duration">
                                    <field name="manual_duration"/>
                                    <field name="min_manual_duration"
                                           attrs="{'invisible': ['|', ('manual_duration', '=', False), ('duration_uom', '!=', 'hours')]}"
                                           widget="float_time"
                                    />
                                    <field name="max_manual_duration"
                                           attrs="{'invisible': ['|', ('manual_duration', '=', False), ('duration_uom', '!=', 'hours')]}"
                                           widget="float_time"
                                    />
                                    <field name="multiple_manual_duration"
                                           attrs="{'invisible': ['|', ('manual_duration', '=', False), ('duration_uom', '!=', 'hours')]}"
                                           widget="float_time"
                                    />
                                    <field name="min_manual_duration_days"
                                           attrs="{'invisible': ['|', ('manual_duration', '=', False), ('duration_uom', '!=', 'days')]}"
                                    />
                                    <field name="max_manual_duration_days"
                                           attrs="{'invisible': ['|', ('manual_duration', '=', False), ('duration_uom', '!=', 'days')]}"
                                    />
                                    <field name="multiple_manual_duration_days"
                                           attrs="{'invisible': ['|', ('manual_duration', '=', False), ('duration_uom', '!=', 'days')]}"
                                    />
                                </group>
                            </group>
                        </page>
                        <page name="description" string="Description">
                            <field name="ba_description" placeholder="description" />
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <record  id="appointment_product_view_kanban" model="ir.ui.view" >
        <field name="name">appointment.product.kanban</field>
        <field name="model">appointment.product</field>
        <field name="arch" type="xml">
            <kanban class="oe_background_grey o_kanban_dashboard" group_create="0" group_delete="0"
                    group_edit="0" archivable="0" quick_create="0">
                <field name="id"/>
                <field name="name"/>
                <field name="color"/>
                <field name="image_128"/>
                <field name="activity_ids"/>
                <field name="activity_state"/>
                <field name="product_id"/>
                <field name="rating_satisfaction"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{kanban_color(record.color.raw_value)} oe_kanban_global_click">
                            <div>
                                <div class="o_kanban_card_content ba_kanban_content">
                                    <div class="o_kanban_image">
                                        <a type="open">
                                            <img t-att-src="kanban_image('appointment.product', 'image_128', record.id.value)"
                                                 class="o_image_64_contain"
                                                 alt="Service Image"
                                            />
                                        </a>
                                    </div>
                                    <div class="o_kanban_primary_left ba_kanban_primary_left">
                                        <div class="o_primary ba_primary">
                                            <span><t t-esc="record.name.value"/></span>
                                        </div>
                                    </div>
                                    <div class="oe_kanban_details ba_kanban_details">
                                        <ul>
                                            <li>
                                                <span>Product: <field name="product_id"/></span>
                                            </li>
                                            <li attrs="{'invisible': [('rating_satisfaction', '=', -1)]}"
                                                groups="business_appointment.group_business_appointment_rating"
                                            >
                                                <a name="%(business_appointment.rating_rating_action_appointment_product)d" type="action">
                                                    <i class="fa fa-smile-o" 
                                                       role="img"
                                                       aria-label="Percentage of satisfaction"
                                                       title="Percentage of satisfaction"/> 
                                                    <t t-esc="record.rating_satisfaction.value"/> %
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="o_kanban_card_manage_pane dropdown-menu" role="menu">
                                    <div class="o_kanban_card_manage_section o_kanban_manage_reports">
                                        <div role="menuitem">
                                            <a type="edit">Edit</a>
                                        </div>
                                        <div role="menuitem">
                                            <a type="delete">Delete</a>
                                        </div>
                                        <div role="menuitem" aria-haspopup="true" class="o_no_padding_kanban_colorpicker">
                                            <ul class="oe_kanban_colorpicker"
                                                data-field="color"
                                                role="popup"
                                            />
                                        </div>
                                    </div>
                                </div>
                                <a class="o_kanban_manage_toggle_button o_left" href="#">
                                    <i class="fa fa-ellipsis-v"
                                       role="img"
                                       aria-label="Manage"
                                       title="Manage"
                                    />
                               </a>
                               <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <div class="o_kanban_inline_block">
                                            <field name="activity_ids" widget="kanban_activity"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="appointment_product_view_tree" model="ir.ui.view">
        <field name="name">appointment.product.tree</field>
        <field name="model">appointment.product</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle"/>
                <field name='name'/>
            </tree>
        </field>
    </record>
    <record id="appointment_product_action" model="ir.actions.act_window">
        <field name="name">Services</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">appointment.product</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" eval="appointment_product_view_search"/>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">Click 'Create' to add a new service used for appointments</p>
        </field>
    </record>
</odoo>
