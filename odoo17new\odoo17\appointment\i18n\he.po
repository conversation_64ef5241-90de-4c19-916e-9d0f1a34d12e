# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* appointment
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# דוד<PERSON>ל<PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# NoaFarkash, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# yael terner, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-26 16:10+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON> Trigaux, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid " (copy)"
msgstr " (העתק)"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_booking_line.py:0
#, python-format
msgid "\"%(resource_name_list)s\" cannot be used for \"%(appointment_type_name)s\""
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_count
msgid "# Appointments"
msgstr "מס' פגישות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_count_report
msgid "# Appointments in the last 30 days"
msgstr "# פגישות ב-30 הימים האחרונים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__calendar_event_count
msgid "# Bookings"
msgstr "# פגישות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_invite_count
msgid "# Invitation Links"
msgstr "# קישורי הזמנה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_resource_count
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_count
msgid "# Resources"
msgstr "# משאבים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_staff_user_count
#: model:ir.model.fields,field_description:appointment.field_appointment_type__staff_user_count
msgid "# Staff Users"
msgstr "# משתמשי צוות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "#{day['today_cls'] and 'Today' or ''}"
msgstr "#{day['today_cls'] and 'היום' or ''}"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "%(appointment_name)s with %(partner_name)s"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "%(attendee_name)s - %(appointment_name)s Booking"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_resource.py:0
#, python-format
msgid "%(original_name)s (copy)"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "%s - Let's meet"
msgstr "%s - בואו ניפגש"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "(Total:"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/calendar.py:0
#, python-format
msgid ", All Day"
msgstr ", כל היום"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_svg.xml:0
#, python-format
msgid ""
".stgrey0{fill:#E3E3E3}\n"
"                .stgrey1{fill:#F2F2F2}"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid ""
"<br/>\n"
"                                        <span>Duration</span>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid ""
"<br/>\n"
"                                    <span>(Last 30 Days)</span>"
msgstr ""

#. module: appointment
#: model:mail.template,body_html:appointment.attendee_invitation_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br><br>\n"
"\n"
"        <t t-if=\"target_customer\">\n"
"            Your appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> <t t-if=\"object.event_id.appointment_type_id.category != 'custom' and object.event_id.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.event_id.user_id.name or ''\">Ready Mat</t></t> has been booked.\n"
"            <t t-if=\"object.state != 'accepted' and object.event_id.appointment_type_id.schedule_based_on == 'resources' and object.event_id.appointment_type_id.resource_manual_confirmation\">\n"
"                You will receive a mail of confirmation with more details when your appointment will be confirmed.\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                <t t-out=\"customer.name or ''\"></t> scheduled the following appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> with you.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Your appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> has been booked.\n"
"            </t>\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"object.state != 'accepted'\">\n"
"            <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Accept</a>\n"
"            <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Decline</a>\n"
"        </t>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\"><t t-out=\"'Reschedule' if target_customer else 'View'\">View</t></a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <li>Appointment Type: <t t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</t></li>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <li t-if=\"object.event_id.appointment_type_id.resource_manage_capacity\">\n"
"                    For: <t t-out=\"object.event_id.resource_total_capacity_reserved\"></t> people\n"
"                </li>\n"
"                <li t-if=\"object.event_id.appointment_type_id.assign_method != 'time_auto_assign'\">\n"
"                    Resources\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.event_id.appointment_resource_ids\" t-as=\"resource\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"resource.name or ''\">Table 1</span>\n"
"                        </li>\n"
"                    </ul>\n"
"                </li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_booked_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <p>\n"
"    Appointment booked for <t t-out=\"object.appointment_type_id.name or ''\">Technical Demo</t>\n"
"    <t t-if=\"object.appointment_type_id.category != 'custom' and object.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t>.\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/join?token={{ object.access_token }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Join</a>\n"
"        <a t-attf-href=\"/web?#id={{ object.id }}&amp;view_type=form&amp;model=calendar.event\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;d&quot;, lang_code=object.env.lang) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div>\n"
"                            <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t>\n"
"                        </div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"></t>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"></td>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <p><strong>Details of the event</strong></p>\n"
"                <ul>\n"
"                    <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                    </li>\n"
"                    <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                    <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                    <li>Attendees\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                            <t t-if=\"attendee.common_name\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <span style=\"margin-left:5px\">You</span>\n"
"                            </t>\n"
"                        </li>\n"
"                    </ul></li>\n"
"                    <li t-if=\"object.appointment_type_id.resource_manage_capacity\">\n"
"                        For: <t t-out=\"object.resource_total_capacity_reserved\"></t> people\n"
"                    </li>\n"
"                    <li t-if=\"object.appointment_type_id.assign_method != 'time_auto_assign'\">\n"
"                        Resources\n"
"                        <ul>\n"
"                            <li t-foreach=\"object.appointment_resource_ids\" t-as=\"resource\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"resource.name or ''\">Table 1</span>\n"
"                            </li>\n"
"                        </ul>\n"
"                    </li>\n"
"                    <li t-if=\"object.videocall_redirection\">\n"
"                        How to Join:\n"
"                        <t t-if=\"object.videocall_source == 'discuss'\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-attf-href=\"{{ object.videocall_redirection }}\" target=\"_blank\" t-out=\"object.videocall_redirection or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </ul>\n"
"                <t t-if=\"not is_html_empty(object.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.description\"></t></li>\n"
"                </t>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_canceled_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <p>\n"
"    The appointment for <t t-out=\"object.appointment_type_id.name or ''\">Technical Demo</t> <t t-if=\"object.appointment_type_id.category != 'custom' and object.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t> has been canceled.\n"
"    </p>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div><t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t></div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"></t>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"></td>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <del>\n"
"                    <p><strong>Details of the event</strong></p>\n"
"                    <ul>\n"
"                            <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                            </li>\n"
"                            <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                            <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                        <li>Attendees\n"
"                        <ul t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <li>\n"
"                                <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                                <t t-if=\"attendee.common_name\">\n"
"                                    <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\"></span>\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <span style=\"margin-left:5px\">You</span>\n"
"                                </t>\n"
"                            </li>\n"
"                        </ul></li>\n"
"                        <li t-if=\"object.videocall_location\">\n"
"                            How to Join:\n"
"                            <t t-if=\"object.get_base_url() in object.videocall_location\"> Join with Odoo Discuss</t>\n"
"                            <t t-else=\"\"> Join at</t><br>\n"
"                            <a t-attf-href=\"{{ object.videocall_location }}\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                        </li>\n"
"                    </ul>\n"
"                    <t t-if=\"not is_html_empty(object.description)\">\n"
"                        <li>Description of the event:\n"
"                        <t t-out=\"object.description\"></t></li>\n"
"                    </t>\n"
"                </del>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "<i class=\"fa fa-info-circle\" title=\"Info\"/>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid ""
"<i class=\"fa fa-pencil me-2\" role=\"img\" aria-label=\"Edit\" "
"title=\"Create custom questions in backend\"/>Add Custom Questions"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "<i class=\"fa fa-plus me-1\"/> Add Guests"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-plus me-1\"/>Add Guests"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_details
msgid ""
"<i class=\"fa fa-video-camera fa-fw me-2 mt-1 text-muted\"/>\n"
"                <span class=\"o_not_editable\">Online</span>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid ""
"<i class=\"fa fa-warning me-2\"/>\n"
"                    <span invisible=\"schedule_based_on != 'users'\">Impossible to share a link for an appointment type that has no user assigned.</span>\n"
"                    <span invisible=\"schedule_based_on != 'resources'\">Impossible to share a link for an appointment type that has no resource assigned.</span>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid ""
"<i class=\"fa fa-warning me-2\"/>\n"
"                    <span invisible=\"schedule_based_on != 'users'\">You need to be part of an appointment type to be able to share a personal link.</span>\n"
"                    <span invisible=\"schedule_based_on != 'resources'\">You can't create a personal link for an appointment type based on resources.</span>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_date
msgid "<small class=\"text-uppercase text-muted\">Date &amp; time</small>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_details
msgid "<small class=\"text-uppercase text-muted\">Meeting details</small>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"d-block\">Scheduled</span>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-globe\"/> Preview"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-pencil\"/> Edit"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-share-alt\"/> Share"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-trash\"/> Delete"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid ""
"<span class=\"px-2\" invisible=\"start_datetime or category == "
"'anytime'\">or</span>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "<span> hours</span>"
msgstr "<span> שעות</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<span>Online</span>"
msgstr "<span>מקוון</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid ""
"<span>You are scheduling a booking outside the available hours of</span>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "<span>people</span>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment canceled!</strong>\n"
"                                            You can schedule another appointment from here."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                                            The selected timeslot is not available anymore.\n"
"                                            Someone has booked the same time slot a few\n"
"                                            seconds before you."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                                            The selected timeslot is not available.\n"
"                                            It appears you already have another meeting with us at that date."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Booked for: </strong>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Contact Information</strong>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Email: </strong>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Name: </strong>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Phone: </strong>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Start Date: </strong>"
msgstr "<strong>תאריך התחלה: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>תאריך סיום: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Type: </strong>"
msgstr "<strong>סוג: </strong>"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "A %s appointment type shouldn't be limited by datetimes."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/calendar_view.py:0
#, python-format
msgid ""
"A list of slots information is needed to create a custom appointment type"
msgstr "צריך מידע על רשימת משבצות ליצירת סוג פגישה מותאם אישית."

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid ""
"A punctual appointment type should be limited between a start and end "
"datetime."
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__access_token
msgid "Access Token"
msgstr "אסימון גישה"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/kanban/kanban_record.xml:0
#, python-format
msgid "Action"
msgstr "פעולה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__active
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__active
#: model:ir.model.fields,field_description:appointment.field_appointment_type__active
msgid "Active"
msgstr "פעיל"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/gantt/gantt_renderer.js:0
#: code:addons/appointment/static/src/views/gantt/gantt_renderer.xml:0
#: code:addons/appointment/static/src/views/list/list_renderer.js:0
#: code:addons/appointment/static/src/views/list/list_renderer.xml:0
#, python-format
msgid "Add Closing Day(s)"
msgstr "סגירת יום (ימים)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Add Guests"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated_card
msgid "Add a function here..."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated_card
msgid "Add a resource description here..."
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Add a specific appointment"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Add an intro message here..."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Add more details about you"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_manage_leaves
msgid "Add or remove leaves from appointments"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Add to Google Agenda"
msgstr "הוסף ליומן Google"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Add to iCal/Outlook"
msgstr "הוסף ל iCal/Outlook"

#. module: appointment
#: model:res.groups,name:appointment.group_appointment_manager
msgid "Administrator"
msgstr "מנהל מערכת"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "הכל"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_report_all
#: model:ir.ui.menu,name:appointment.menu_schedule_report_all_events
msgid "All Appointments"
msgstr "כל הפגישות"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__allday
#, python-format
msgid "All day"
msgstr "כל היום"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Allow Cancelling"
msgstr "אפשר ביטול"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__allow_guests
msgid "Allow Guests"
msgstr "אפשר אורחים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__country_ids
msgid "Allowed Countries"
msgstr "מדינות מותרות"

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_answer_input_value_check
msgid "An answer input must either have a text value or a predefined answer."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/calendar_view.py:0
#, python-format
msgid "An appointment type is needed to get the link."
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_calendar_event_check_resource_and_appointment_type
msgid "An event cannot book resources without an appointment type."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_slot.py:0
#, python-format
msgid "An unique type slot should have a start and end datetime"
msgstr "משבצת מסוג ייחודי צריכה מועד התחלה וסיום"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__name
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_view_form
msgid "Answer"
msgstr "תשובה"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_answer_input_action_from_question
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_graph
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_pivot
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_tree
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Answer Breakdown"
msgstr "פירוט תשובות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_form
msgid "Answer Input"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Answers"
msgstr "תשובות"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__anytime
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
#, python-format
msgid "Any Time"
msgstr "כל זמן"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_invite__resources_choice__all_assigned_resources
msgid "Any User/Resource"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Anytime appointment types should only have one user but got %s users"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_id
#, python-format
msgid "Appointment"
msgstr "פגישה"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_answer_input
msgid "Appointment Answer Inputs"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_answer_input_ids
msgid "Appointment Answers"
msgstr "תשובות פגישה"

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_booked
#: model:mail.message.subtype,name:appointment.mt_appointment_type_booked
#: model:mail.message.subtype,name:appointment.mt_calendar_event_booked
msgid "Appointment Booked"
msgstr "פגישות שנקבעו"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_booked_mail_template
msgid "Appointment Booked: {{ object.appointment_type_id.name }}"
msgstr "נקבעה פגישה: {{ object.appointment_type_id.name }}"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_booking_line
msgid "Appointment Booking Line"
msgstr ""

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_canceled
#: model:mail.message.subtype,name:appointment.mt_appointment_type_canceled
#: model:mail.message.subtype,name:appointment.mt_calendar_event_canceled
msgid "Appointment Canceled"
msgstr "פגישה בוטלה"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_canceled_mail_template
msgid "Appointment Canceled: {{ object.appointment_type_id.name }}"
msgstr "בוטל פגישה: {{ object.appointment_type_id.name }}"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form
msgid "Appointment Details"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_duration_formatted
msgid "Appointment Duration Formatted "
msgstr "פורמט משך הפגישה"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__appointment_duration_formatted
msgid "Appointment Duration formatted in words"
msgstr "פורמט משך הפגישה במילים"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Appointment Duration should be higher than 0.00."
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_invite_id
msgid "Appointment Invitation"
msgstr "הזמנה לפגישה"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_search
msgid "Appointment Invitation Links"
msgstr "קישורים להזמנה לפגישה"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree_invitation
msgid "Appointment Invitations"
msgstr "הזמנות לפגישות"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_invite
msgid "Appointment Invite"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__meeting_ids
msgid "Appointment Meetings"
msgstr "פגישות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointment Name"
msgstr "שם הפגישה"

#. module: appointment
#: model:onboarding.onboarding,name:appointment.onboarding_onboarding_appointment
msgid "Appointment Onboarding"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_answer
msgid "Appointment Question Answers"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_question
msgid "Appointment Questions"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_resource
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__appointment_resource_id
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__name
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_resource_id
msgid "Appointment Resource"
msgstr ""

#. module: appointment
#: model:ir.actions.server,name:appointment.resource_calendar_leaves_action_show_appointment_resources
msgid "Appointment Resource Leaves"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_resource_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
msgid "Appointment Resources"
msgstr "משאבי פגישות"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_resource_action
msgid ""
"Appointment Resources are the places or equipment people can book\n"
"                (e.g. Tables, Tennis Courts, Meeting Rooms, ...)"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__name
msgid "Appointment Title"
msgstr "כותרת הפגישה"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_type
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_appointment_question__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__appointment_type_id
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_search
msgid "Appointment Type"
msgstr "סוג פגישה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__appointment_type_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Appointment Types"
msgstr "סוגי פגישות"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Appointment canceled"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Appointment canceled by: %(partners)s"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Appointment re-booked"
msgstr ""

#. module: appointment
#: model:mail.template,name:appointment.appointment_booked_mail_template
msgid "Appointment: Appointment Booked"
msgstr ""

#. module: appointment
#: model:mail.template,name:appointment.appointment_canceled_mail_template
msgid "Appointment: Appointment Canceled"
msgstr ""

#. module: appointment
#: model:mail.template,name:appointment.attendee_invitation_mail_template
msgid "Appointment: Attendee Invitation"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_slot
msgid "Appointment: Time Slot"
msgstr ""

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_type_action
#: model:ir.actions.act_window,name:appointment.calendar_event_action_appointment_reporting
#: model:ir.ui.menu,name:appointment.appointment_menu_calendar
#: model:ir.ui.menu,name:appointment.appointment_type_menu
#: model:ir.ui.menu,name:appointment.main_menu_appointments
#: model:ir.ui.menu,name:appointment.menu_schedule_report_all
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_graph
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_pivot
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_search
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_menu_appointment
msgid "Appointments"
msgstr "פגישות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointments by"
msgstr "פגישות מאת"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Archived"
msgstr "בארכיון"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__resources_choice
msgid "Assign to"
msgstr "משויך ל"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__assign_method
msgid "Assignment Method"
msgstr "שיטת הקצאה"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_slot.py:0
#, python-format
msgid ""
"At least one slot duration is shorter than the meeting duration (%s hours)"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Attendees"
msgstr "משתתפים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_attended
msgid "Attendees Arrived"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__slot_ids
msgid "Availabilities"
msgstr "זמינות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__schedule_based_on
#: model:ir.model.fields,field_description:appointment.field_appointment_type__schedule_based_on
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_schedule_based_on
msgid "Availability on"
msgstr "זמינות על"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__answer_ids
msgid "Available Answers"
msgstr "תשובות זמינות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
msgid "Available In"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__appointment_type_ids
msgid "Available in"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_1920
msgid "Avatar"
msgstr "אווטר"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_1024
msgid "Avatar 1024"
msgstr "אווטר 1024"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_128
msgid "Avatar 128"
msgstr "אווטר 128"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_256
msgid "Avatar 256"
msgstr "אווטר 256"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_512
msgid "Avatar 512"
msgstr "אווטר 512"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Back to edit mode"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__base_book_url
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__base_book_url
msgid "Base Link URL"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__calendar_event_ids
msgid "Booked Appointments"
msgstr "הזמינו פגישות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Booked by"
msgstr "הוזמן ע\"י"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__calendar_event_id
msgid "Booking"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "Booking Details"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__event_stop
msgid "Booking End"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__booking_line_ids
msgid "Booking Lines"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Booking Name"
msgstr "שם"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__event_start
msgid "Booking Start"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Bookings"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_partner_ids
msgid "CC to"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "מידע על משתתפי לוח השנה"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_event
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__calendar_event_id
msgid "Calendar Event"
msgstr "אירוע לוח שנה"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_onboarding_link_view_form
msgid "Cancel"
msgstr "בטל"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__min_cancellation_hours
msgid "Cancel Before (hours)"
msgstr "בטל לפני (שעות)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Cancel/Reschedule"
msgstr "בטל/ קבע פגישה חדשה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__canceled_mail_template_id
msgid "Cancelation Email"
msgstr "דוא\"ל לביטול"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__capacity
msgid "Capacity"
msgstr "קיבולת"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_manual_confirmation_percentage
msgid "Capacity Percentage"
msgstr "אחוז קיבולת"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__capacity_reserved
msgid "Capacity Reserved"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__capacity_used
msgid "Capacity Used"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__capacity_reserved
msgid "Capacity reserved by the user"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__capacity_used
msgid "Capacity that will be used based on the capacity and resource selected"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__category
msgid "Category"
msgstr "קטגוריה"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__checkbox
msgid "Checkboxes (multiple answers)"
msgstr "תיבות סימון (תשובות מרובות)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Choose your appointment"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__company_id
msgid "Company"
msgstr "חברה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__disable_save_button
msgid "Computes if alert is present"
msgstr ""

#. module: appointment
#: model:ir.ui.menu,name:appointment.appointment_menu_config
msgid "Configuration"
msgstr "תצורה"

#. module: appointment
#: model:onboarding.onboarding.step,button_text:appointment.appointment_onboarding_create_appointment_type_step
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Configure"
msgstr "הגדר"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_invite_action
msgid ""
"Configure links that allow booking appointments with custom settings<br>\n"
"                (e.g. a specific user only, a list of appointment types, ...)"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_resources.xml:0
#, python-format
msgid "Confirm"
msgstr "אשר"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Confirm Appointment"
msgstr "אשר פגישה"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/gantt/gantt_popover.xml:0
#, python-format
msgid "Confirm Check-In"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__booked_mail_template_id
msgid "Confirmation Email"
msgstr "דוא\"ל אישור"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_confirmation
msgid "Confirmation Message"
msgstr "הודעת אישור"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Confirmed"
msgstr "מאושר"

#. module: appointment
#: model:onboarding.onboarding.step,button_text:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "Connect"
msgstr "התחבר"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Connect your Calendar"
msgstr "חבר את לוח השנה שלך"

#. module: appointment
#: model:onboarding.onboarding.step,title:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "Connect your calendar"
msgstr "חבר את היומן שלך"

#. module: appointment
#: model:ir.model,name:appointment.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Contact Details:"
msgstr "פרטי איש קשר:"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_partner_ids
msgid ""
"Contacts that need to be notified whenever a new appointment is booked or "
"canceled,                                                  regardless of "
"whether they attend or not"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Continue <span class=\"oi oi-arrow-right\"/>"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.js:0
#, python-format
msgid "Copied!"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/components/appointment_invite/appointment_invite_copy_close.xml:0
#: code:addons/appointment/static/src/components/appointment_onboarding/appointment_onboarding_invite_buttons.xml:0
#, python-format
msgid "Copy Link & Close"
msgstr "העתק קישור וסגור"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Create Closing Day(s)"
msgstr "צור סגירת יום (ימים)"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_resource_action
msgid "Create an Appointment Resource"
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action_custom
msgid ""
"Create invites on the fly from your calendar and share them with anyone by "
"using the Share Availabilities button."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Create your first Appointment"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_question__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_type__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_question__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_type__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__custom
msgid "Custom"
msgstr "מותאם"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Custom Link"
msgstr "צור קישור"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__partner_id
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Customer"
msgstr "לקוח"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL APPOINTMENTS"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#, python-format
msgid "Date"
msgstr "תאריך"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Date &amp; time"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Dates"
msgstr "תאריכים"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Declined"
msgstr "דחייה"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Default slots cannot be applied to the %s appointment type category."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__slot_type
msgid ""
"Defines the type of slot. The recurring slot is the default type which is used for\n"
"        appointment type that are used recurringly in type like medical appointment.\n"
"        The one shot type is only used when an user create a custom appointment type for a client by\n"
"        defining non-recurring time slot (e.g. 10th of April 2021 from 10 to 11 am) from its calendar."
msgstr ""
"מגדיר את סוג המשבצת, ברירת המחדל היא אירוע מחזורי, המשמש\n"
"        לסוג אירועים שחוזר על עצמו, כמו תור לרופה.\n"
"        חד-פעמי מיועד למשתמש שרוצה ליצור סוג אירוע על-ידי\n"
"        בחירת משבצת זמן במועד מסויים (למשל ה-20 באפריל 2016 מ-16:20-17:20)."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__event_videocall_source
msgid ""
"Defines the type of video call link that will be used for the generated "
"events. Keep it empty to prevent generating meeting url."
msgstr ""

#. module: appointment
#: model:appointment.type,name:appointment.appointment_type_dental_care
msgid "Dental Care"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__description
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "Description"
msgstr "תיאור"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__destination_resource_ids
msgid "Destination combination"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Details"
msgstr "פרטים"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__allday
msgid ""
"Determine if the slot englobe the whole day, mainly used for unique slot "
"type"
msgstr "קובע אם המשבצת תופסת את כל היום. בעיקר מיועד למשבצת זמן ייחודית."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form_insert_link
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
#, python-format
msgid "Discard"
msgstr "בטל"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_question__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_type__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__avatars_display
msgid "Display the Users'/Resources' picture on the Website."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__resource_manual_confirmation
msgid ""
"Do not automatically accept meetings created from the appointment once the total capacity\n"
"            reserved for a slot exceeds the percentage chosen. The appointment is still considered as reserved for\n"
"            the slots availability."
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__select
msgid "Dropdown (one answer)"
msgstr "נפתח (תשובה אחת)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__duration
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_duration
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Duration"
msgstr "משך זמן"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Email*"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Email: %(email)s"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/appointment.py:0
#, python-format
msgid "Email: %s"
msgstr "מייל: %s"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__leave_end_dt
msgid "End Date"
msgstr "תאריך סיום"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__end_datetime
msgid "End Datetime"
msgstr "מועד סיום"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__end_datetime
msgid "End datetime for unique slot type management"
msgstr "זמן סיום לניהול משבצות זמן ייחודיות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__end_hour
msgid "Ending Hour"
msgstr "שעת סיום"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_alarm
msgid "Event Alarm"
msgstr "התראת אירוע"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "Event Details"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Every"
msgstr "כל"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Extra Comments..."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Extra Message on Confirmation"
msgstr "הודעה נוספת לאחר אישור פגישה"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_confirmation
msgid "Extra information provided once the appointment is booked."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_appointment
msgid "Follow, reschedule or cancel your appointments"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "For"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__5
msgid "Friday"
msgstr "יום שישי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "From"
msgstr "מ"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__avatars_display
msgid "Front-End Display"
msgstr "תצוגה קדמית"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Full name*"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Get Share Link"
msgstr "קישור לשיתוף"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#: model_terms:ir.ui.view,arch_db:appointment.appointment_onboarding_link_view_form
#, python-format
msgid "Get Your Link"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_onboarding_link
msgid "Get a link to an appointment type during the onboarding"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_invite__suggested_staff_user_ids
msgid ""
"Get the users linked to the appointment type selected to apply a domain on "
"the users that can be selected"
msgstr ""

#. module: appointment
#: model:onboarding.onboarding.step,title:appointment.appointment_onboarding_preview_invite_step
msgid "Get your link"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Google Agenda"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Group By"
msgstr "קבץ לפי"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Guest usage is limited to 10 customers for performance reason."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Guests"
msgstr "אורחים"

#. module: appointment
#: model:ir.model,name:appointment.model_ir_http
msgid "HTTP Routing"
msgstr "ניתוב HTTP"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "How to join"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__assign_method
msgid ""
"How users and resources will be assigned to meetings customers book on your "
"website."
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__id
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__id
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__id
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__id
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__id
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__id
#: model:ir.model.fields,field_description:appointment.field_appointment_question__id
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__id
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__id
#: model:ir.model.fields,field_description:appointment.field_appointment_type__id
msgid "ID"
msgstr "מזהה"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_has_error
#: model:ir.model.fields,help:appointment.field_appointment_type__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "If empty, Odoo will not send emails"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__restrict_to_resource_ids
msgid ""
"If empty, all resources are considered to be available.\n"
"If set, only the selected resources will be taken into account for this slot."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__restrict_to_user_ids
msgid ""
"If empty, all users are considered to be available.\n"
"If set, only the selected users will be taken into account for this slot."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "If empty, the meeting is considered as taking place online"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__canceled_mail_template_id
msgid ""
"If set an email will be sent to the customer when the appointment is "
"canceled."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__booked_mail_template_id
msgid ""
"If set an email will be sent to the customer when the appointment is "
"confirmed."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"אם השדה הפעיל מוגדר כלא נכון, הוא יאפשר לך להסתיר את רשומת המשאב מבלי להסיר "
"אותה."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"אם השדה הפעיל מוגדר כ- לא נכון, הוא יאפשר להסתיר את מידע אזעקת האירוע מבלי "
"להסיר אותו."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_1920
msgid "Image"
msgstr "תמונה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_1024
msgid "Image 1024"
msgstr "תמונה 1024"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_128
msgid "Image 128"
msgstr "תמונה 128"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_256
msgid "Image 256"
msgstr "תמונה 256"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_512
msgid "Image 512"
msgstr "תמונה 512"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Insert Appointment Link"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form_insert_link
msgid "Insert link"
msgstr "הוסף קישור"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_intro
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Introduction Message"
msgstr "הודעת פתיחה"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/appointment_form.js:0
#: code:addons/appointment/static/src/js/appointment_validation.js:0
#, python-format
msgid "Invalid Email"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_invite_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_tree
msgid "Invitation Links"
msgstr "קישורי הזמנה"

#. module: appointment
#: model:mail.template,description:appointment.attendee_invitation_mail_template
msgid "Invitation email to new attendees of an appointment"
msgstr ""

#. module: appointment
#: model:mail.template,subject:appointment.attendee_invitation_mail_template
msgid "Invitation to {{ object.event_id.name }}"
msgstr "הזמנה לארוע {{ object.event_id.name }}"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_type_action_custom
#: model:ir.ui.menu,name:appointment.menu_appointment_type_custom
msgid "Invitations"
msgstr "הזמנות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__is_published
msgid "Is Published"
msgstr "מפורסם"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"It's too late to cancel online, please contact the attendees another way if "
"you really can't make it."
msgstr ""
"מאוחר מדי לביטול מקוון של הפגישה. נא להודיע למוזמנים בדרך חלופית אם באמת אין"
" באפשרותך להגיע."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Join at"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Join with Odoo Discuss"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__country_ids
msgid ""
"Keep empty to allow visitors from any country, otherwise you only allow "
"visitors from selected countries"
msgstr ""
"השאר ריק כדי לאפשר אורחים מכל הארצות, אחרת תאפשר למבקרים מארצות נבחרות בלבד"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_question__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_type__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_question__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_type__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__allow_guests
msgid "Let attendees invite guests when registering a meeting."
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Link Copied in your clipboard!"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "Link Generator"
msgstr "מחולל קישורים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__book_url
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "Link URL"
msgstr "קישור URL"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/components/appointment_invite/appointment_invite_copy_close.js:0
#: code:addons/appointment/static/src/components/appointment_onboarding/appointment_onboarding_invite_buttons.js:0
#, python-format
msgid "Link copied to clipboard!"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__linked_resource_ids
msgid "Linked Resource"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__linked_resource_ids
msgid "List of resources that can be combined to handle a bigger demand."
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__location_id
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Location"
msgstr "מיקום"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__location
msgid "Location formatted"
msgstr "מיקום בפורמט"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__location
msgid "Location formatted for one line uses"
msgstr "מיקום הפורמט עבור שימושים בשורה אחת"

#. module: appointment
#: model:onboarding.onboarding.step,done_text:appointment.appointment_onboarding_create_appointment_type_step
msgid "Looks great!"
msgstr "נראה נהדר!"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_resources.xml:0
#, python-format
msgid "Make your choice"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_manage_capacity
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_manage_capacity
msgid "Manage Capacities"
msgstr "ניהול יכולות"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__resource_manage_capacity
#: model:ir.model.fields,help:appointment.field_calendar_event__appointment_type_manage_capacity
msgid ""
"Manage the maximum amount of people a resource can handle (e.g. Table for 6 "
"persons, ...)"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_manual_confirmation
msgid "Manual Confirmation"
msgstr "אישור ידני"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Max in"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__capacity
msgid ""
"Maximum amount of people for this resource (e.g. Table for 6 persons, ...)"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_invite__resources_choice__current_user
msgid "Me (only with Users)"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Meet With Me"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__videocall_redirection
msgid "Meeting redirection URL"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "Meetings"
msgstr "פגישות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Message from the Organizer"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Messages"
msgstr "הודעות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Min"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__1
msgid "Monday"
msgstr "יום שני"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "More Options"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__text
msgid "Multi-line text"
msgstr "טקסט מרובה שורות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "My Appointments"
msgstr "הפגישות שלי"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_search
msgid "My Links"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr "שם"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Navigation"
msgstr "ניווט"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_alarm__default_for_new_appointment_type
msgid "New Appointments Default"
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_answer_input_action_from_question
msgid "No Answers yet!"
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action
msgid "No Appointment Configured"
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_view_bookings_resources
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_view_bookings_users
msgid "No Appointment or Resource were found."
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action_custom
msgid "No Custom Availabilities Shared!"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__avatars_display__hide
msgid "No Picture"
msgstr "ללא תמונה"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_invite_action
msgid "No Shared Links yet!"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__appointment_type_info_msg
msgid "No User Assigned Message"
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_appointment_reporting
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report_all
msgid "No data yet!"
msgstr "אין מידע עדיין"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "אף אחד"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_event__alarm_ids
msgid "Notifications sent to all attendees to remind of the meeting."
msgstr "הודעות נשלחות לכל המשתתפים כדי להזכיר את הפגישה."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Number of people"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__event_videocall_source__discuss
msgid "Odoo Discuss"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_onboarding_onboarding
msgid "Onboarding"
msgstr "קליטה לעבודה"

#. module: appointment
#: model:onboarding.onboarding.step,step_image_alt:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "Onboarding Connect your calendar"
msgstr ""

#. module: appointment
#: model:onboarding.onboarding.step,step_image_alt:appointment.appointment_onboarding_preview_invite_step
msgid "Onboarding Get your link"
msgstr ""

#. module: appointment
#: model:onboarding.onboarding.step,step_image_alt:appointment.appointment_onboarding_create_appointment_type_step
msgid "Onboarding Set Your Availabilities"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__slot_type__unique
msgid "One Shot"
msgstr "חד-פעמי"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid ""
"Only letters, numbers, underscores and dashes are allowed in your links."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_invite.py:0
#, python-format
msgid ""
"Only letters, numbers, underscores and dashes are allowed in your links. You"
" need to adapt %s."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Only one anytime appointment type is allowed for a specific user."
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Open"
msgstr "פתח"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "Opening Hours"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
msgid "Operator"
msgstr "מפעיל"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Options"
msgstr "אפשרויות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__user_id
msgid "Organizer"
msgstr "מארגן"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Our first availability is"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Outlook"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Past"
msgstr "עבר"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_booker_id
msgid "Person who is booking the appointment"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Phone number*"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Phone: %(phone)s"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/appointment.py:0
#, python-format
msgid "Phone: %s"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__assign_method__resource_time
msgid "Pick User/Resource then Time"
msgstr "בחר משתמש/ עובד לפני זמן"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Pick a Work Schedule..."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "Pick a schedule to restrict openings"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Pick your availabilities"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__placeholder
msgid "Placeholder"
msgstr "שומר מקום"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Please, select another date."
msgstr "אנא בחר תאריך אחר."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_resource_ids
msgid "Possible resources"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_staff_user_ids
msgid "Possible users"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/components/appointment_onboarding/appointment_onboarding_invite_buttons.xml:0
#: model:onboarding.onboarding.step,button_text:appointment.appointment_onboarding_preview_invite_step
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#, python-format
msgid "Preview"
msgstr "תצוגה מקדימה"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__punctual
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Punctual"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__question_id
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__question_id
#: model:ir.model.fields,field_description:appointment.field_appointment_question__name
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Question"
msgstr "שאלה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__question_type
#: model:ir.model.fields,field_description:appointment.field_appointment_question__question_type
msgid "Question Type"
msgstr "סוג שאלה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__question_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Questions"
msgstr "שאלות"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__radio
msgid "Radio (one answer)"
msgstr "רדיו (תשובה אחת)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__rating_ids
msgid "Ratings"
msgstr "דירוגים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__reason
msgid "Reason"
msgstr "סיבה"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__slot_type__recurring
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__recurring
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Recurring"
msgstr "הזמנות חוזרות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__redirect_url
msgid "Redirect URL"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__reminder_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_event__alarm_ids
#: model:ir.ui.menu,name:appointment.menu_appointment_reminders
msgid "Reminders"
msgstr "תזכורות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Remove"
msgstr "הסר"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_schedule_report
#: model:ir.ui.menu,name:appointment.reporting_menu_calendar
msgid "Reporting"
msgstr "דו\"חות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__question_required
msgid "Required Answer"
msgstr "תשובה חובה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__resource_id
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Resource"
msgstr "עובד"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_view_bookings_resources
#: model:ir.actions.server,name:appointment.calendar_event_action_all_resources_bookings
msgid "Resource Bookings"
msgstr "פגישות עובדים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__calendar_id
msgid "Resource Calendar"
msgstr "לוח שנה של עובד"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_appointment_resource_leaves
msgid "Resource Leaves"
msgstr ""

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_resource_action
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__resource_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resource_ids
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__schedule_based_on__resources
#: model:ir.ui.menu,name:appointment.menu_appointment_resource
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree_invitation
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "Resources"
msgstr "עובדים"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_appointment_schedule_resource_booking
msgid "Resources Bookings"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resources_on_leave
msgid "Resources intersecting with leave time"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Responsible"
msgstr "אחראי"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_tree
msgid "Restrict to Resource"
msgstr "הגבל לעובד"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__restrict_to_resource_ids
msgid "Restrict to Resources"
msgstr "הגבל לעובדים"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_tree
msgid "Restrict to User"
msgstr "הגבל למשתמש"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__restrict_to_user_ids
msgid "Restrict to Users"
msgstr "הגבל למשתמשים"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Restrict to specific Resources"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "SCHEDULED"
msgstr "מתוזמן"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__6
msgid "Saturday"
msgstr "יום שבת"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Save"
msgstr "שמור"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_appointment_schedule_resources
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Schedule"
msgstr "תזמן"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__schedule_based_on
msgid "Schedule Based On"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Schedule an Appointment"
msgstr "תזמן פגישה"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_appointment_reporting
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report_all
msgid "Schedule appointments to get statistics"
msgstr "קבעו פגישות כדי לקבל סטטיסטיקות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__min_schedule_hours
msgid "Schedule before (hours)"
msgstr "תזמן לפני (שעות)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__max_schedule_days
msgid "Schedule not after (days)"
msgstr "תזמן לא אחרי (ימים)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Scheduling"
msgstr "תזמון"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "חפש בהכל"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr "חפש בתיאור"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Name"
msgstr "חיפוש בשם"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Responsible"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Select Dates"
msgstr "בחר תאריכים"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__assign_method__time_resource
msgid "Select Time then User/Resource"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__assign_method__time_auto_assign
msgid "Select Time then auto-assign"
msgstr "בחר זמן ושייך אוטומטית"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Select a date &amp; time"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_slots.xml:0
#, python-format
msgid "Select a time"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Select attendees..."
msgstr "בחר משתתפים..."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__value_answer_id
msgid "Selected Answer"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__appointment_type_count
msgid "Selected Appointments Count"
msgstr "כמות הפגישות שנבחרו"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Selection Questions"
msgstr ""

#. module: appointment
#: model:mail.template,description:appointment.appointment_canceled_mail_template
msgid "Sent to all attendees when an appointment is cancelled"
msgstr ""

#. module: appointment
#: model:mail.template,description:appointment.appointment_booked_mail_template
msgid "Sent to followers of an appointment type when a meeting is booked"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__sequence
#: model:ir.model.fields,field_description:appointment.field_appointment_question__sequence
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__sequence
#: model:ir.model.fields,field_description:appointment.field_appointment_type__sequence
msgid "Sequence"
msgstr "רצף"

#. module: appointment
#: model:onboarding.onboarding.step,title:appointment.appointment_onboarding_create_appointment_type_step
msgid "Set your availabilities"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree
msgid "Share"
msgstr "שתף"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Share Appointment Link"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Share Availabilities"
msgstr "שתף זמינות"

#. module: appointment
#. odoo-javascript
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.js:0
#, python-format
msgid "Share Link"
msgstr "שתף קישור"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_invite_action
#: model:ir.ui.menu,name:appointment.menu_appointment_invite
msgid "Share Links"
msgstr "שתף קישורים"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action
msgid ""
"Share calendar link allowing people to book meetings with you, your team or "
"a resource."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_onboarding_link_view_form
msgid ""
"Share this link to let people book meetings with you, your team or a "
"resource."
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__shareable
msgid "Shareable"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Shared Links"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__short_code
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__short_code
msgid "Short Code"
msgstr "קוד קצר"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__short_code_format_warning
msgid "Short Code Format Warning"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__short_code_unique_warning
msgid "Short Code Unique Warning"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__avatars_display__show
msgid "Show Pictures"
msgstr "עם תמונה"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__char
msgid "Single line text"
msgstr "טקסט שורה בודדת"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__slot_type
msgid "Slot type"
msgstr "סוג משבצת"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_intro
msgid "Small description of the appointment type."
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry,"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, it is no longer possible to schedule an appointment."
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, there is not any more availability for the asked capacity."
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, we have no availability for an appointment."
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, we have no more slots available for this month."
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__source_resource_ids
msgid "Source combination"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__appointment_resource_ids
msgid "Specific Resources"
msgstr "עובדים ספציפיים"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_invite__resources_choice__specific_resources
msgid "Specific Users/Resources"
msgstr ""

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_view_bookings_users
#: model:ir.actions.server,name:appointment.calendar_event_action_all_users_appointments
#: model:ir.ui.menu,name:appointment.menu_appointment_schedule_staff_appointment
msgid "Staff Bookings"
msgstr "פגישות צוות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__leave_start_dt
msgid "Start Date"
msgstr "תאריך תחילה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__start_datetime
msgid "Start Datetime"
msgstr "מועד התחלה"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__event_start
msgid "Start date of an event, without time for full days events"
msgstr "תאריך התחלה של אירוע, ללא זמן לאירועים של ימי מלאים"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__start_datetime
msgid "Start datetime for unique slot type management"
msgstr "זמן התחלה לניהול משבצות זמן ייחודיות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__start_hour
msgid "Starting Hour"
msgstr "שעת התחלה"

#. module: appointment
#: model:onboarding.onboarding.step,done_text:appointment.appointment_onboarding_configure_calendar_provider_step
#: model:onboarding.onboarding.step,done_text:appointment.appointment_onboarding_preview_invite_step
msgid "Step Completed!"
msgstr "שלב הושלם!"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__event_stop
msgid "Stop date of an event, without time for full days events"
msgstr "תאריך עצירה של אירוע, ללא זמן לאירועים של ימי מלאים"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Subject"
msgstr "נושא"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__answer_input_ids
msgid "Submitted Answers"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__7
msgid "Sunday"
msgstr "יום ראשון"

#. module: appointment
#: model:appointment.question,name:appointment.appointment_type_dental_care_question_1
msgid "Symptoms"
msgstr "סימפטומים"

#. module: appointment
#: model:appointment.type,name:appointment.appointment_type_tennis_court
msgid "Tennis Court"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__value_text_box
msgid "Text Answer"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Text Questions"
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_invite_short_code_uniq
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "The URL is already taken, please pick another code."
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_type_check_resource_manual_confirmation_percentage
msgid "The capacity percentage should be between 0 and 100%"
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_booking_line_check_capacity_reserved
msgid "The capacity reserved should be positive."
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_booking_line_check_capacity_used
msgid "The capacity used can not be lesser than the capacity reserved"
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_slot_check_start_and_end_hour
msgid "The end time must be later than the start time."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "The event %s cannot book resources without an appointment type."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "The field '%s' does not exist in the targeted model"
msgstr "השדה '%s' לא מוגדר במודל היעד"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_invite.py:0
#, python-format
msgid "The following appointment type(s) have no resource assigned: %s."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_invite.py:0
#, python-format
msgid "The following appointment type(s) have no staff assigned: %s."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_question.py:0
#, python-format
msgid "The following question(s) do not have any selectable answers : %s"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid ""
"The following users are in restricted slots but they are not part of the "
"available staff: %s"
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_resource_check_capacity
msgid "The resource should have at least one capacity."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__sequence
msgid ""
"The sequence dictates if the resource is going to be picked in higher priority against another resource\n"
"        (e.g. for 2 tables of 4, the lowest sequence will be picked first)"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Their first availability is"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "There is currently no appointment available"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "There is no appointment linked to your account."
msgstr "אין פגישות מקושרות לחשבונך."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__shareable
msgid ""
"This allows to share the resource with multiple attendee for a same time "
"slot (e.g. a bar counter)"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it does not have any "
"opening hours configured"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no resource "
"assigned"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no resource "
"assigned and does not have any opening hours configured"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no staff assigned"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no staff assigned"
" and does not have any opening hours configured"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr "שדה זה משמש כדי להגדיר באיזה אזור זמן המשאבים יעבדו,"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_edit_in_backend
msgid "This is a preview of the customer appointment form."
msgstr "זה תצוגה מקדימה של טופס פגישה ללקוח."

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__4
msgid "Thursday"
msgstr "יום חמישי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__tz
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_tz
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Timezone"
msgstr "אזור זמן"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__appointment_tz
msgid "Timezone where appointment take place"
msgstr "אזור הזמן בו מתקיימת הפגישה"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Timezone:"
msgstr "אזור זמן:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "To"
msgstr "ל"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__access_token
msgid "Token"
msgstr "אסימון"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "Total"
msgstr "סה\"כ"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_total_capacity
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_tree
msgid "Total Capacity"
msgstr "קיבולת כוללת"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resource_total_capacity_reserved
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Total Capacity Reserved"
msgstr "קיבולת כוללת שמורה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resource_total_capacity_used
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Total Capacity Used"
msgstr "קיבולת כוללת בשימוש"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Total Reserved"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Total:"
msgstr "סה\"כ:"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__2
msgid "Tuesday"
msgstr "יום שלישי"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Type"
msgstr "סוג"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Uncertain"
msgstr "לא בטוח"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/gantt/gantt_popover.xml:0
#, python-format
msgid "Unconfirm Check-In"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Until"
msgstr "עד"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Until (max)"
msgstr "עד (מקסימום)"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Upcoming"
msgstr "בקרוב"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_alarm__default_for_new_appointment_type
msgid "Use as default for new Appointment Types"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__category
msgid ""
"Used to define this appointment type's category.\n"
"\n"
"        Can be one of:\n"
"\n"
"            - Recurring: the default category, weekly recurring slots. Accessible from the website\n"
"\n"
"            - Punctual: recurring slots limited between 2 datetimes. Accessible from the website\n"
"\n"
"            - Custom: the user will create and share to another user a custom appointment type with hand-picked time slots\n"
"\n"
"            - Anytime: the user will create and share to another user an appointment type covering all their time slots"
msgstr ""

#. module: appointment
#: model:res.groups,name:appointment.group_appointment_user
msgid "User"
msgstr "משתמש"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__staff_user_ids
#: model:ir.model.fields,field_description:appointment.field_appointment_type__staff_user_ids
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__schedule_based_on__users
msgid "Users"
msgstr "משתמשים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__event_videocall_source
msgid "Videoconference Link"
msgstr "קישור ועידת וידאו"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__3
msgid "Wednesday"
msgstr "יום רביעי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__weekday
msgid "Week Day"
msgstr "יום"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__resource_calendar_id
msgid ""
"If kept empty, the working schedule of the company set on the resource will "
"be used"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.staff_user_select
msgid "With"
msgstr "עם"

#. module: appointment
#: model:onboarding.onboarding.step,description:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "With Outlook or Google"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__resource_calendar_id
msgid "Working Hours"
msgstr "שעות עבודה "

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/appointment_form.js:0
#: code:addons/appointment/static/src/js/appointment_validation.js:0
#, python-format
msgid "You cannot invite more than 10 people"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_details_column
msgid "Your Appointment"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"Your appointment has been reserved! We will come back to you to confirm it."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Your appointment has successfully been booked!"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Your appointment is in less than"
msgstr "הפגישה שלך היא בפחות מ-"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
msgid "Your choice"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "days"
msgstr "ימים"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"During this meeting, we will...\""
msgstr "לדוגמא \"במהלך הפגישה, אנחנו...\""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"I feel nauseous...\""
msgstr "לְמָשָׁל \"יש לי בחילה...\""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "e.g. \"John Doe - Tennis Court Booking\""
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"Technical Demo\""
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"Thank you for your trust, we look forward to meeting you!\""
msgstr "למשל \"תודה על האמון, מצפים לפגוש אותך!\""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"What are your symptoms?\""
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "e.g. +1(605)691-3277"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "e.g. Inventory count and valuation"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "e.g. John Smith"
msgstr "למשל: ג'ון סמית'"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "e.g. Tennis Court 1"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"e.g. <EMAIL>\r\n"
"e.g. <EMAIL>\r\n"
"..."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid ""
"e.g. <EMAIL> \r\n"
"e.g. <EMAIL>\r\n"
"..."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "e.g. <EMAIL>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "from"
msgstr "מ"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "has no availability for an appointment."
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "has no more slots available for this month."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "hours before"
msgstr "שעות לפני"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "hours from now!"
msgstr "שעות מעכשיו!"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_details
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "people"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "persons)"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "somebody"
msgstr ""

#. module: appointment
#: model:onboarding.onboarding.step,description:appointment.appointment_onboarding_create_appointment_type_step
msgid "to automate appointments"
msgstr ""

#. module: appointment
#: model:onboarding.onboarding.step,description:appointment.appointment_onboarding_preview_invite_step
msgid "to schedule appointments"
msgstr "לקביעת פגישות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "total capacity"
msgstr "קיבולת כוללת"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "when over"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "with"
msgstr "עם"
