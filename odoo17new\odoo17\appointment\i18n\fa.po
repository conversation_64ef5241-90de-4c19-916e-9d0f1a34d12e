# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* appointment
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <y.shad<PERSON><PERSON>@gmail.com>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <ifara<PERSON><EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-26 16:10+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid " (copy)"
msgstr " (کپی)"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_booking_line.py:0
#, python-format
msgid "\"%(resource_name_list)s\" cannot be used for \"%(appointment_type_name)s\""
msgstr ""
"نمی‌توان از %(resource_name_list)s برای %(appointment_type_name)s استفاده "
"کرد"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_count
msgid "# Appointments"
msgstr "# قرار ملاقات‌ها"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_count_report
msgid "# Appointments in the last 30 days"
msgstr "# قرار ملاقات‌های 30 روز اخیر"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__calendar_event_count
msgid "# Bookings"
msgstr "# ملاقات‌ها"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_invite_count
msgid "# Invitation Links"
msgstr "# لینک‌های دعوت"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_resource_count
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_count
msgid "# Resources"
msgstr "# منابع"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_staff_user_count
#: model:ir.model.fields,field_description:appointment.field_appointment_type__staff_user_count
msgid "# Staff Users"
msgstr "# کارکنان"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "#{day['today_cls'] and 'Today' or ''}"
msgstr "#{روز['today_cls'] و «امروز» یا}"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "%(appointment_name)s with %(partner_name)s"
msgstr "%(appointment_name)s با %(partner_name)s"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "%(attendee_name)s - %(appointment_name)s Booking"
msgstr "قرار ملاقات %(attendee_name)s - %(appointment_name)s"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_resource.py:0
#, python-format
msgid "%(original_name)s (copy)"
msgstr "%(original_name)s (کپی)"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "%s - Let's meet"
msgstr "%s - ملاقات را شروع کنیم"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "(Total:"
msgstr "(مجموع:"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/calendar.py:0
#, python-format
msgid ", All Day"
msgstr "، تمام روز"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_svg.xml:0
#, python-format
msgid ""
".stgrey0{fill:#E3E3E3}\n"
"                .stgrey1{fill:#F2F2F2}"
msgstr ""
".stgrey0{fill:#E3E3E3}\n"
"                .stgrey1{fill:#F2F2F2}"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid ""
"<br/>\n"
"                                        <span>Duration</span>"
msgstr ""
"<br/>\n"
"                                        <span>مدت</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid ""
"<br/>\n"
"                                    <span>(Last 30 Days)</span>"
msgstr ""
"<br/>\n"
"                                    <span>(30 روز اخیر)</span>"

#. module: appointment
#: model:mail.template,body_html:appointment.attendee_invitation_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br><br>\n"
"\n"
"        <t t-if=\"target_customer\">\n"
"            Your appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> <t t-if=\"object.event_id.appointment_type_id.category != 'custom' and object.event_id.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.event_id.user_id.name or ''\">Ready Mat</t></t> has been booked.\n"
"            <t t-if=\"object.state != 'accepted' and object.event_id.appointment_type_id.schedule_based_on == 'resources' and object.event_id.appointment_type_id.resource_manual_confirmation\">\n"
"                You will receive a mail of confirmation with more details when your appointment will be confirmed.\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                <t t-out=\"customer.name or ''\"></t> scheduled the following appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> with you.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Your appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> has been booked.\n"
"            </t>\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"object.state != 'accepted'\">\n"
"            <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Accept</a>\n"
"            <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Decline</a>\n"
"        </t>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\"><t t-out=\"'Reschedule' if target_customer else 'View'\">View</t></a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <li>Appointment Type: <t t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</t></li>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <li t-if=\"object.event_id.appointment_type_id.resource_manage_capacity\">\n"
"                    For: <t t-out=\"object.event_id.resource_total_capacity_reserved\"></t> people\n"
"                </li>\n"
"                <li t-if=\"object.event_id.appointment_type_id.assign_method != 'time_auto_assign'\">\n"
"                    Resources\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.event_id.appointment_resource_ids\" t-as=\"resource\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"resource.name or ''\">Table 1</span>\n"
"                        </li>\n"
"                    </ul>\n"
"                </li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br><br>\n"
"\n"
"        <t t-if=\"target_customer\">\n"
"            قرار ملاقات شما<strong t-out=\"object.event_id.appointment_type_id.name or ''\">یک نسخه‌ی نمایشی را ایجاد کنید</strong> <t t-if=\"object.event_id.appointment_type_id.category != 'custom' and object.event_id.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.event_id.user_id.name or ''\">Ready Mat</t></t>رزرو شده است.\n"
"            <t t-if=\"object.state != 'accepted' and object.event_id.appointment_type_id.schedule_based_on == 'resources' and object.event_id.appointment_type_id.resource_manual_confirmation\">\n"
"                هنگامی که قرار ملاقات شما تأیید شود یک ایمیل تأیید حجاوی جزئیات بیشتر دریافت می‌کنید.\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                <t t-out=\"customer.name or ''\"></t> sملاقات زیر را برنامه‌ریزی کنید <strong t-out=\"object.event_id.appointment_type_id.name or ''\">یک نسخه‌ی آزمایشی ایجاد کنید</strong> with you.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                قرار ملاقات شما <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> رزرو شده است.\n"
"            </t>\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"object.state != 'accepted'\">\n"
"            <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Accept</a>\n"
"            <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                لغو</a>\n"
"        </t>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\"><t t-out=\"'Reschedule' if target_customer else 'View'\">View</t></a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <li>Appointment Type: <t t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</t></li>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.name or ''\">یک هفته یک بار برای 3 رویداد</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>شرکت‌کنندگان\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <li t-if=\"object.event_id.appointment_type_id.resource_manage_capacity\">\n"
"                    For: <t t-out=\"object.event_id.resource_total_capacity_reserved\"></t> people\n"
"                </li>\n"
"                <li t-if=\"object.event_id.appointment_type_id.assign_method != 'time_auto_assign'\">\n"
"                    Resources\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.event_id.appointment_resource_ids\" t-as=\"resource\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"resource.name or ''\">Table 1</span>\n"
"                        </li>\n"
"                    </ul>\n"
"                </li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>شرح رویداد:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting گفت و گو برای قیمت‌گذاری جدید محصولات و خدمات.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_booked_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <p>\n"
"    Appointment booked for <t t-out=\"object.appointment_type_id.name or ''\">Technical Demo</t>\n"
"    <t t-if=\"object.appointment_type_id.category != 'custom' and object.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t>.\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/join?token={{ object.access_token }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Join</a>\n"
"        <a t-attf-href=\"/web?#id={{ object.id }}&amp;view_type=form&amp;model=calendar.event\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;d&quot;, lang_code=object.env.lang) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div>\n"
"                            <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t>\n"
"                        </div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"></t>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"></td>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <p><strong>Details of the event</strong></p>\n"
"                <ul>\n"
"                    <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                    </li>\n"
"                    <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                    <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                    <li>Attendees\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                            <t t-if=\"attendee.common_name\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <span style=\"margin-left:5px\">You</span>\n"
"                            </t>\n"
"                        </li>\n"
"                    </ul></li>\n"
"                    <li t-if=\"object.appointment_type_id.resource_manage_capacity\">\n"
"                        For: <t t-out=\"object.resource_total_capacity_reserved\"></t> people\n"
"                    </li>\n"
"                    <li t-if=\"object.appointment_type_id.assign_method != 'time_auto_assign'\">\n"
"                        Resources\n"
"                        <ul>\n"
"                            <li t-foreach=\"object.appointment_resource_ids\" t-as=\"resource\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"resource.name or ''\">Table 1</span>\n"
"                            </li>\n"
"                        </ul>\n"
"                    </li>\n"
"                    <li t-if=\"object.videocall_redirection\">\n"
"                        How to Join:\n"
"                        <t t-if=\"object.videocall_source == 'discuss'\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-attf-href=\"{{ object.videocall_redirection }}\" target=\"_blank\" t-out=\"object.videocall_redirection or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </ul>\n"
"                <t t-if=\"not is_html_empty(object.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.description\"></t></li>\n"
"                </t>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <p>\n"
"   رزرو ملاقات برای <t t-out=\"object.appointment_type_id.name or ''\">نسخه‌ی فنی آزمایشی</t>\n"
"    <t t-if=\"object.appointment_type_id.category != 'custom' and object.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t>.\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/join?token={{ object.access_token }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Join</a>\n"
"        <a t-attf-href=\"/web?#id={{ object.id }}&amp;view_type=form&amp;model=calendar.event\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;d&quot;, lang_code=object.env.lang) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div>\n"
"                            <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t>\n"
"                        </div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"></t>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"></td>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <p><strong>Details of the event</strong></p>\n"
"                <ul>\n"
"                    <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                    </li>\n"
"                    <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">هر یک هفته یک بار برای 3 رویداد</t></li>\n"
"                    <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                    <li>شرکت‌کنندگان\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                            <t t-if=\"attendee.common_name\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <span style=\"margin-left:5px\">شما</span>\n"
"                            </t>\n"
"                        </li>\n"
"                    </ul></li>\n"
"                    <li t-if=\"object.appointment_type_id.resource_manage_capacity\">\n"
"                        For: <t t-out=\"object.resource_total_capacity_reserved\"></t> people\n"
"                    </li>\n"
"                    <li t-if=\"object.appointment_type_id.assign_method != 'time_auto_assign'\">\n"
"                        Resources\n"
"                        <ul>\n"
"                            <li t-foreach=\"object.appointment_resource_ids\" t-as=\"resource\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"resource.name or ''\">Table 1</span>\n"
"                            </li>\n"
"                        </ul>\n"
"                    </li>\n"
"                    <li t-if=\"object.videocall_redirection\">\n"
"                        How to Join:\n"
"                        <t t-if=\"object.videocall_source == 'discuss'\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-attf-href=\"{{ object.videocall_redirection }}\" target=\"_blank\" t-out=\"object.videocall_redirection or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </ul>\n"
"                <t t-if=\"not is_html_empty(object.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.description\"></t></li>\n"
"                </t>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_canceled_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <p>\n"
"    The appointment for <t t-out=\"object.appointment_type_id.name or ''\">Technical Demo</t> <t t-if=\"object.appointment_type_id.category != 'custom' and object.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t> has been canceled.\n"
"    </p>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div><t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t></div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"></t>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"></td>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <del>\n"
"                    <p><strong>Details of the event</strong></p>\n"
"                    <ul>\n"
"                            <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                            </li>\n"
"                            <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                            <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                        <li>Attendees\n"
"                        <ul t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <li>\n"
"                                <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                                <t t-if=\"attendee.common_name\">\n"
"                                    <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\"></span>\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <span style=\"margin-left:5px\">You</span>\n"
"                                </t>\n"
"                            </li>\n"
"                        </ul></li>\n"
"                        <li t-if=\"object.videocall_location\">\n"
"                            How to Join:\n"
"                            <t t-if=\"object.get_base_url() in object.videocall_location\"> Join with Odoo Discuss</t>\n"
"                            <t t-else=\"\"> Join at</t><br>\n"
"                            <a t-attf-href=\"{{ object.videocall_location }}\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                        </li>\n"
"                    </ul>\n"
"                    <t t-if=\"not is_html_empty(object.description)\">\n"
"                        <li>Description of the event:\n"
"                        <t t-out=\"object.description\"></t></li>\n"
"                    </t>\n"
"                </del>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <p>\n"
"    The appointment for <t t-out=\"object.appointment_type_id.name or ''\">نسخه‌ی فنی آزمایشی</t> <t t-if=\"object.appointment_type_id.category != 'custom' and object.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t> has been canceled.\n"
"    </p>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div><t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t></div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"></t>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"></td>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <del>\n"
"                    <p><strong>Details of the event</strong></p>\n"
"                    <ul>\n"
"                            <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                            </li>\n"
"                            <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">هر یک هفته یک بار برای 3 رویداد</t></li>\n"
"                            <li t-if=\"not object.allday and object.duration\">مدت: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                        <li>شرکت‌کنندگان\n"
"                        <ul t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <li>\n"
"                                <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                                <t t-if=\"attendee.common_name\">\n"
"                                    <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\"></span>\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <span style=\"margin-left:5px\">You</span>\n"
"                                </t>\n"
"                            </li>\n"
"                        </ul></li>\n"
"                        <li t-if=\"object.videocall_location\">\n"
"                            How to Join:\n"
"                            <t t-if=\"object.get_base_url() in object.videocall_location\"> Join with Odoo Discuss</t>\n"
"                            <t t-else=\"\"> Join at</t><br>\n"
"                            <a t-attf-href=\"{{ object.videocall_location }}\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                        </li>\n"
"                    </ul>\n"
"                    <t t-if=\"not is_html_empty(object.description)\">\n"
"                        <li>شرح رویداد:\n"
"                        <t t-out=\"object.description\"></t></li>\n"
"                    </t>\n"
"                </del>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "<i class=\"fa fa-info-circle\" title=\"Info\"/>"
msgstr "<i class=\"fa fa-info-circle\" title=\"Info\"/>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid ""
"<i class=\"fa fa-pencil me-2\" role=\"img\" aria-label=\"Edit\" "
"title=\"Create custom questions in backend\"/>Add Custom Questions"
msgstr ""
"<i class=\"fa fa-pencil me-2\" role=\"img\" aria-label=\"Edit\" "
"title=\"Create custom questions in backend\"/>سوالات مروبطه را وارد کنید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "<i class=\"fa fa-plus me-1\"/> Add Guests"
msgstr "<i class=\"fa fa-plus me-1\"/> افزودن مهمانان"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-plus me-1\"/>Add Guests"
msgstr "<i class=\"fa fa-plus me-1\"/>افزودن مهمانان"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_details
msgid ""
"<i class=\"fa fa-video-camera fa-fw me-2 mt-1 text-muted\"/>\n"
"                <span class=\"o_not_editable\">Online</span>"
msgstr ""
"<i class=\"fa fa-video-camera fa-fw me-2 mt-1 text-muted\"/>\n"
"                <span class=\"o_not_editable\">آنلاین</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid ""
"<i class=\"fa fa-warning me-2\"/>\n"
"                    <span invisible=\"schedule_based_on != 'users'\">Impossible to share a link for an appointment type that has no user assigned.</span>\n"
"                    <span invisible=\"schedule_based_on != 'resources'\">Impossible to share a link for an appointment type that has no resource assigned.</span>"
msgstr ""
"<i class=\"fa fa-warning me-2\"/>\n"
"                    <span invisible=\"schedule_based_on != 'users'\">Iبه اشتراک گذاشتن لینک برای قرار ملاقاتی که هیچ کاربری به ان اختصاص داده نشده غیرممکن است.</span>\n"
"                    <span invisible=\"schedule_based_on != 'resources'\">.</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid ""
"<i class=\"fa fa-warning me-2\"/>\n"
"                    <span invisible=\"schedule_based_on != 'users'\">You need to be part of an appointment type to be able to share a personal link.</span>\n"
"                    <span invisible=\"schedule_based_on != 'resources'\">You can't create a personal link for an appointment type based on resources.</span>"
msgstr ""
"<i class=\"fa fa-warning me-2\"/>\n"
"                    <span invisible=\"schedule_based_on != 'users'\">You برای به اشتراک گذاری یک لینک شخصی باید عضوی از قرار ملاقات باشید.</span>\n"
"                    <span invisible=\"schedule_based_on != 'resources'\">نمی‌توانید لینک شخصی را برای یک قرار ملاقات مبتنی بر منابع ایجاد کنید.</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_date
msgid "<small class=\"text-uppercase text-muted\">Date &amp; time</small>"
msgstr "<small class=\"text-uppercase text-muted\">Date &amp; time</small>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_details
msgid "<small class=\"text-uppercase text-muted\">Meeting details</small>"
msgstr "<small class=\"text-uppercase text-muted\">جزئیات جلسه</small>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"d-block\">Scheduled</span>"
msgstr "<span class=\"d-block\">برنامه‌ریزی شده</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-globe\"/> Preview"
msgstr "<span class=\"fa fa-globe\"/> پیش‌نمایش"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-pencil\"/> Edit"
msgstr "<span class=\"fa fa-pencil\"/> ویرایش"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-share-alt\"/> Share"
msgstr "<span class=\"fa fa-share-alt\"/> اشتراک‌گذاری"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-trash\"/> Delete"
msgstr "<span class=\"fa fa-trash\"/> حذف"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid ""
"<span class=\"px-2\" invisible=\"start_datetime or category == "
"'anytime'\">or</span>"
msgstr ""
"<span class=\"px-2\" invisible=\"start_datetime or category == "
"'anytime'\">or</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">بایگانی شده</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "<span> hours</span>"
msgstr "<span> ساعت</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<span>Online</span>"
msgstr "<span>آنلاین</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid ""
"<span>You are scheduling a booking outside the available hours of</span>"
msgstr ""
"<span>شما در حال برنامه‌ریزی یک قرار ملاقات خارج از ساعات موجود هستید "
"of</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "<span>people</span>"
msgstr "<span>افراد</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment canceled!</strong>\n"
"                                            You can schedule another appointment from here."
msgstr ""
"<strong>قرار ملاقات لغو شد!</strong>\n"
"                                            از اینجا می‌توانید قرار ملاقات دیگری را برنامه‌ریزی کنید."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                                            The selected timeslot is not available anymore.\n"
"                                            Someone has booked the same time slot a few\n"
"                                            seconds before you."
msgstr ""
"<strong>قرار ملاقات لغو شد!</strong>\n"
"                                            بازه‌ی زمانی برنامه‌ریزی شده دیگر موجود نیست.\n"
"                                            فرد دیگری چند ثانیه قبل از شما همین بازه‌ی زمانی را رزرو کرده است."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                                            The selected timeslot is not available.\n"
"                                            It appears you already have another meeting with us at that date."
msgstr ""
"<strong>قرار ملاقات لغو شد!</strong>\n"
"                                            بازه‌ی زمانی انتخاب شده موجود نیست.\n"
"                                            به نظر می‌رسد در آن تاریخ شما جلسه‌ی دیگری با ما دارید ."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Booked for: </strong>"
msgstr "<strong>رزرو برای: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Contact Information</strong>"
msgstr "<strong>اطلاعات تماس</strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Email: </strong>"
msgstr "<strong>ایمیل: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Name: </strong>"
msgstr "<strong>نام: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Phone: </strong>"
msgstr "<strong>شماره تلفن: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Start Date: </strong>"
msgstr "<strong>تاریخ شروع: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>تاریخ توقف: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Type: </strong>"
msgstr "<strong>نوع: </strong>"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "A %s appointment type shouldn't be limited by datetimes."
msgstr "یک قرارملاقات %s نباید محدود به زمان یا تاریخ باشد."

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/calendar_view.py:0
#, python-format
msgid ""
"A list of slots information is needed to create a custom appointment type"
msgstr "فهرستی از اطلاعات مختلف برای ایجاد یک قرار ملاقات اختصاصی لازم است"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid ""
"A punctual appointment type should be limited between a start and end "
"datetime."
msgstr "یک قرار ملاقات دقیق باید دارای زمان شروع و پایان باشد."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__access_token
msgid "Access Token"
msgstr "توکن دسترسی"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/kanban/kanban_record.xml:0
#, python-format
msgid "Action"
msgstr "اقدام"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_needaction
msgid "Action Needed"
msgstr "اقدام مورد نیاز است"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__active
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__active
#: model:ir.model.fields,field_description:appointment.field_appointment_type__active
msgid "Active"
msgstr "فعال"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/gantt/gantt_renderer.js:0
#: code:addons/appointment/static/src/views/gantt/gantt_renderer.xml:0
#: code:addons/appointment/static/src/views/list/list_renderer.js:0
#: code:addons/appointment/static/src/views/list/list_renderer.xml:0
#, python-format
msgid "Add Closing Day(s)"
msgstr "روز(های) پایان را اضافه کنید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Add Guests"
msgstr "مهمانان را اضافه کنید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated_card
msgid "Add a function here..."
msgstr "کارکردی را در اینجا اضافه کنید ..."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated_card
msgid "Add a resource description here..."
msgstr "در اینجا شرح منابع را اضافه کنید ..."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Add a specific appointment"
msgstr "یک قرار ملاقات مشخص را اضافه کنید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Add an intro message here..."
msgstr "در اینجا یک پیام معرفی را اضافه کنید ..."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Add more details about you"
msgstr "جزئیات بیشتری را در مورد خودتان اضافه کنید"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_manage_leaves
msgid "Add or remove leaves from appointments"
msgstr "مرخصی‌ها را به قرار ملاقات‌ها اضافه یا حذف کنید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Add to Google Agenda"
msgstr "افزودن به دستور کار گوگل"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Add to iCal/Outlook"
msgstr "افزودن به iCall/Outlook"

#. module: appointment
#: model:res.groups,name:appointment.group_appointment_manager
msgid "Administrator"
msgstr "مدیر"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "همه"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_report_all
#: model:ir.ui.menu,name:appointment.menu_schedule_report_all_events
msgid "All Appointments"
msgstr "همه‌ی قرار‌های ملاقات"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__allday
#, python-format
msgid "All day"
msgstr "همه روزها"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Allow Cancelling"
msgstr "اجازه‌ی لغو"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__allow_guests
msgid "Allow Guests"
msgstr "اجازه دادن به کاربر مهمان"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__country_ids
msgid "Allowed Countries"
msgstr "کشورهای مجاز"

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_answer_input_value_check
msgid "An answer input must either have a text value or a predefined answer."
msgstr "یک پاسخ باید هم شامل یک ارزش متنی باشد و هم یک پاسخ از پیش تعیین شده."

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/calendar_view.py:0
#, python-format
msgid "An appointment type is needed to get the link."
msgstr "تعیین نوع قرار ملاقات برای دریافت لینک ضروری است."

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_calendar_event_check_resource_and_appointment_type
msgid "An event cannot book resources without an appointment type."
msgstr "یک رویداد نمی‌تواند منابع را بدون تعیین نوع قرار ملاقات رزرو کند."

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_slot.py:0
#, python-format
msgid "An unique type slot should have a start and end datetime"
msgstr "یک قرار ملاقات منحصر به فرد باید دارای تاریخ و زمان شروع و پایان باشد"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__name
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_view_form
msgid "Answer"
msgstr "پاسخ"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_answer_input_action_from_question
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_graph
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_pivot
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_tree
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Answer Breakdown"
msgstr "تجزیه پاسخ"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_form
msgid "Answer Input"
msgstr "ورود پاسخ"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Answers"
msgstr "پاسخ‌ها"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__anytime
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
#, python-format
msgid "Any Time"
msgstr "هر زمان"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_invite__resources_choice__all_assigned_resources
msgid "Any User/Resource"
msgstr "هر کاربر/منبع"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Anytime appointment types should only have one user but got %s users"
msgstr ""
"قرار ملاقات‌هایی که دارای زمان مشخصی نیستند تنها باید یک کاربر داشته باشند "
"نه چند کاربر %s"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_id
#, python-format
msgid "Appointment"
msgstr "قرار ملاقات"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_answer_input
msgid "Appointment Answer Inputs"
msgstr "ورودی‌های مربوط به پاسخ قرار ملاقات"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_answer_input_ids
msgid "Appointment Answers"
msgstr "پاسخ‌های مربوط به قرار ملاقات"

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_booked
#: model:mail.message.subtype,name:appointment.mt_appointment_type_booked
#: model:mail.message.subtype,name:appointment.mt_calendar_event_booked
msgid "Appointment Booked"
msgstr "قرار ملاقات‌های رزرو شده"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_booked_mail_template
msgid "Appointment Booked: {{ object.appointment_type_id.name }}"
msgstr "قرار ملاقات‌‎های رزرو شده: {{object.appointment_type_id.name }}"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_booking_line
msgid "Appointment Booking Line"
msgstr "سطر رزرو قرار ملاقات"

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_canceled
#: model:mail.message.subtype,name:appointment.mt_appointment_type_canceled
#: model:mail.message.subtype,name:appointment.mt_calendar_event_canceled
msgid "Appointment Canceled"
msgstr "قرار ملاقات لغو شد"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_canceled_mail_template
msgid "Appointment Canceled: {{ object.appointment_type_id.name }}"
msgstr "قرار ملاقات کنسل شده: {{ object.appointment_type_id.name }}"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form
msgid "Appointment Details"
msgstr "جزئیات قرار ملاقات"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_duration_formatted
msgid "Appointment Duration Formatted "
msgstr "تنظیم مدت قرار ملاقات"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__appointment_duration_formatted
msgid "Appointment Duration formatted in words"
msgstr "تنظیم مدت قرار ملاقات به حروف"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Appointment Duration should be higher than 0.00."
msgstr "مدت قرار ملاقات باید بیشتر از 0.00 باشد."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_invite_id
msgid "Appointment Invitation"
msgstr "دعوت‌نامه‌ی قرار ملاقات"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_search
msgid "Appointment Invitation Links"
msgstr "لینک‌های دعوت‌نامه‌ی قرار ملاقات"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree_invitation
msgid "Appointment Invitations"
msgstr "دعوت‌های قرار ملاقات"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_invite
msgid "Appointment Invite"
msgstr "دعوت برای قرار ملاقات"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__meeting_ids
msgid "Appointment Meetings"
msgstr "جلسات قرار ملاقات"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointment Name"
msgstr "نام قرار ملاقات"

#. module: appointment
#: model:onboarding.onboarding,name:appointment.onboarding_onboarding_appointment
msgid "Appointment Onboarding"
msgstr "وارد کردن قرار ملاقات"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_answer
msgid "Appointment Question Answers"
msgstr "پاسخ‌‌های مربوط به قرار ملاقات"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_question
msgid "Appointment Questions"
msgstr "سوالات مربوط به قرار ملاقات"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_resource
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__appointment_resource_id
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__name
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_resource_id
msgid "Appointment Resource"
msgstr "منبع قرار ملاقات"

#. module: appointment
#: model:ir.actions.server,name:appointment.resource_calendar_leaves_action_show_appointment_resources
msgid "Appointment Resource Leaves"
msgstr "نبود منابع قرار ملاقات"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_resource_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
msgid "Appointment Resources"
msgstr "شرکت‌کنندگان قرار ملاقات"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_resource_action
msgid ""
"Appointment Resources are the places or equipment people can book\n"
"                (e.g. Tables, Tennis Courts, Meeting Rooms, ...)"
msgstr ""
"منابع قرار ملاقات‌ها مکان‌ها یا تجهیزاتی هستند که امکان ثبت آنها وجود دارد "
"(مانند میزها، زمین‌های تنیس، اتاق‌های جلسه، ...)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__name
msgid "Appointment Title"
msgstr "عنوان قرار ملاقات"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_type
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_appointment_question__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__appointment_type_id
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_search
msgid "Appointment Type"
msgstr "نوع قرار ملاقات"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__appointment_type_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Appointment Types"
msgstr "انواع قرار ملاقات"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Appointment canceled"
msgstr "قرار ملاقات لغو شد"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Appointment canceled by: %(partners)s"
msgstr "لغو قرار ملاقات توسط: %(partners)s"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Appointment re-booked"
msgstr "رزرو مجدد قرار ملاقات"

#. module: appointment
#: model:mail.template,name:appointment.appointment_booked_mail_template
msgid "Appointment: Appointment Booked"
msgstr "قرار ملاقات: قرار ملاقات رزرو شده"

#. module: appointment
#: model:mail.template,name:appointment.appointment_canceled_mail_template
msgid "Appointment: Appointment Canceled"
msgstr "قرار ملاقات: دعوت‌نامه‌ی قرار ملاقات"

#. module: appointment
#: model:mail.template,name:appointment.attendee_invitation_mail_template
msgid "Appointment: Attendee Invitation"
msgstr "قرار ملاقات: دعوت‌نامه‌ی شرکت‌کننده"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_slot
msgid "Appointment: Time Slot"
msgstr "قرار ملاقات: بازه‌ی زمانی"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_type_action
#: model:ir.actions.act_window,name:appointment.calendar_event_action_appointment_reporting
#: model:ir.ui.menu,name:appointment.appointment_menu_calendar
#: model:ir.ui.menu,name:appointment.appointment_type_menu
#: model:ir.ui.menu,name:appointment.main_menu_appointments
#: model:ir.ui.menu,name:appointment.menu_schedule_report_all
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_graph
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_pivot
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_search
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_menu_appointment
msgid "Appointments"
msgstr "قرار ملاقات‌ها"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointments by"
msgstr "قرارهای ملاقات با"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Archived"
msgstr "بایگانی شده"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__resources_choice
msgid "Assign to"
msgstr "محول کردن به"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__assign_method
msgid "Assignment Method"
msgstr "روش واگذاری"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_slot.py:0
#, python-format
msgid ""
"At least one slot duration is shorter than the meeting duration (%s hours)"
msgstr "حداقل یک بازه‌ی زمانی کوتاه‌تر از مدت جلسه است (%s ساعات)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_attachment_count
msgid "Attachment Count"
msgstr "تعداد پیوست ها"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Attendees"
msgstr "شرکت‌کنندگان"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_attended
msgid "Attendees Arrived"
msgstr "شرکت‌کنندگان رسیدند"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__slot_ids
msgid "Availabilities"
msgstr "ساعات در دسترس بودن"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__schedule_based_on
#: model:ir.model.fields,field_description:appointment.field_appointment_type__schedule_based_on
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_schedule_based_on
msgid "Availability on"
msgstr "موجود در تاریخ"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__answer_ids
msgid "Available Answers"
msgstr "پاسخ‌های موجود "

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
msgid "Available In"
msgstr "موجود در "

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__appointment_type_ids
msgid "Available in"
msgstr "موجود در"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_1920
msgid "Avatar"
msgstr "آواتار"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_1024
msgid "Avatar 1024"
msgstr "آواتار 1024"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_128
msgid "Avatar 128"
msgstr "آواتار 128"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_256
msgid "Avatar 256"
msgstr "آواتار 256"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_512
msgid "Avatar 512"
msgstr "آواتار 512"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Back to edit mode"
msgstr "بازگشت به حالت ویرایش"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__base_book_url
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__base_book_url
msgid "Base Link URL"
msgstr "نشانی لینک مبدأ"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__calendar_event_ids
msgid "Booked Appointments"
msgstr "قرار ملاقات‌های رزرو شده"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Booked by"
msgstr "رزرو شده توسط"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__calendar_event_id
msgid "Booking"
msgstr "رزرو کردن"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "Booking Details"
msgstr "جزئیات رزرو"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__event_stop
msgid "Booking End"
msgstr "پایان رزرو"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__booking_line_ids
msgid "Booking Lines"
msgstr "سطرهای رزرو"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Booking Name"
msgstr "نام رزرو"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__event_start
msgid "Booking Start"
msgstr "شروع رزرو"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Bookings"
msgstr "رزروها"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_partner_ids
msgid "CC to"
msgstr "ایمیل ارسال شده به"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "اطلاعات شرکت کنندگان در تقویم"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_event
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__calendar_event_id
msgid "Calendar Event"
msgstr "رخداد گاهشمار"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_onboarding_link_view_form
msgid "Cancel"
msgstr "لغو"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__min_cancellation_hours
msgid "Cancel Before (hours)"
msgstr "لغو قبل از (ساعت)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Cancel/Reschedule"
msgstr "لغو/برنامه‌ریزی مجدد"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__canceled_mail_template_id
msgid "Cancelation Email"
msgstr "ایمیل لغو"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__capacity
msgid "Capacity"
msgstr "ظرفیت"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_manual_confirmation_percentage
msgid "Capacity Percentage"
msgstr "درصد ظرفیت"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__capacity_reserved
msgid "Capacity Reserved"
msgstr "ظرفیت رزرو شده"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__capacity_used
msgid "Capacity Used"
msgstr "ظرفیت مورد استفاده"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__capacity_reserved
msgid "Capacity reserved by the user"
msgstr "ظرفیت رزرو شده توسط کاربر"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__capacity_used
msgid "Capacity that will be used based on the capacity and resource selected"
msgstr "ظرفیتی که بر اساس ظرفیت و منابع انتخاب شده مورد استفاده قرار می‌گیرد"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__category
msgid "Category"
msgstr "دسته بندی"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__checkbox
msgid "Checkboxes (multiple answers)"
msgstr "چک‌باکس (چند پاسخه)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Choose your appointment"
msgstr "قرار ملاقات موردنظر خود را انتخاب کنید"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__company_id
msgid "Company"
msgstr "شرکت"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__disable_save_button
msgid "Computes if alert is present"
msgstr "در صورت دریافت هشدار محاسبه می‌کند"

#. module: appointment
#: model:ir.ui.menu,name:appointment.appointment_menu_config
msgid "Configuration"
msgstr "پیکربندی"

#. module: appointment
#: model:onboarding.onboarding.step,button_text:appointment.appointment_onboarding_create_appointment_type_step
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Configure"
msgstr "پیکربندی"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_invite_action
msgid ""
"Configure links that allow booking appointments with custom settings<br>\n"
"                (e.g. a specific user only, a list of appointment types, ...)"
msgstr ""
"لینک‌هایی را پیکربندی می‌کند که امکان رزرو قرار ملاقات با تنظیمات اختصاصی را"
" فراهم می‌کنند<br> (به طورمثال تنها برای یک کاربر، فهرستی از انواع قرار "
"ملاقات‌ها و ...)"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_resources.xml:0
#, python-format
msgid "Confirm"
msgstr "تایید کردن"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Confirm Appointment"
msgstr "تأیید قرار ملاقات"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/gantt/gantt_popover.xml:0
#, python-format
msgid "Confirm Check-In"
msgstr "تأیید پذیرش"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__booked_mail_template_id
msgid "Confirmation Email"
msgstr "ایمیل تایید"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_confirmation
msgid "Confirmation Message"
msgstr "پیغام تایید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Confirmed"
msgstr "تایید شده"

#. module: appointment
#: model:onboarding.onboarding.step,button_text:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "Connect"
msgstr "اتصال"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Connect your Calendar"
msgstr "تقویم خود را وصل کنید"

#. module: appointment
#: model:onboarding.onboarding.step,title:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "Connect your calendar"
msgstr "به گاهشمار خود متصل شوید"

#. module: appointment
#: model:ir.model,name:appointment.model_res_partner
msgid "Contact"
msgstr "مخاطب"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Contact Details:"
msgstr "جزییات مخاطب:"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_partner_ids
msgid ""
"Contacts that need to be notified whenever a new appointment is booked or "
"canceled,                                                  regardless of "
"whether they attend or not"
msgstr ""
"مخاطبینی که باید از رزرو یا لغو شدن یک قرار ملاقات مطلع شوند، صرف نظر اینکه "
"حضور دارند یا نه"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Continue <span class=\"oi oi-arrow-right\"/>"
msgstr "ادامه دهید <span class=\"oi oi-arrow-right\"/>"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.js:0
#, python-format
msgid "Copied!"
msgstr "کپی شده!"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/components/appointment_invite/appointment_invite_copy_close.xml:0
#: code:addons/appointment/static/src/components/appointment_onboarding/appointment_onboarding_invite_buttons.xml:0
#, python-format
msgid "Copy Link & Close"
msgstr "لینک را کپی کرده و ببندید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Create Closing Day(s)"
msgstr "ایجاد روز(های) پایانی"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_resource_action
msgid "Create an Appointment Resource"
msgstr "ایجاد یک منبع قرار ملاقات"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action_custom
msgid ""
"Create invites on the fly from your calendar and share them with anyone by "
"using the Share Availabilities button."
msgstr ""
"ایجاد دعوت‌نامه‌ی فوری و به اشتراک‌گذاری آنها با همه از طریق دکمه‌ی "
"قابلیت‌های به اشتراک‌گذاری"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Create your first Appointment"
msgstr "اولین قرار ملاقات خود را ایجاد کنید"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_question__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_type__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_question__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_type__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__custom
msgid "Custom"
msgstr "سفارشی"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Custom Link"
msgstr "لینک سفارشی‌سازی شده"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__partner_id
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Customer"
msgstr "مشتری"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL APPOINTMENTS"
msgstr ""
"زیرساخت‌ها را اینجا قرار دهید تا در تمام قرار ملاقات‌ها قابل دسترسی باشند"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#, python-format
msgid "Date"
msgstr "تاریخ"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Date &amp; time"
msgstr "تاریخ و زمان"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Dates"
msgstr "تاریخ ها"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Declined"
msgstr "رد شد"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Default slots cannot be applied to the %s appointment type category."
msgstr "نمی‌توان بازه‌های زمانی پیشفرض را برای قرار ملاقات‌های %s اعمال کرد"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__slot_type
msgid ""
"Defines the type of slot. The recurring slot is the default type which is used for\n"
"        appointment type that are used recurringly in type like medical appointment.\n"
"        The one shot type is only used when an user create a custom appointment type for a client by\n"
"        defining non-recurring time slot (e.g. 10th of April 2021 from 10 to 11 am) from its calendar."
msgstr ""
"نوع بازه‌ی زمانی را مشخص کنید. بازه‌ی زمانی تکراری یک بازه‌ی پیشفرض است که برای قرار ملاقات‌های مکرر مورداستفاده قرار می‌گیرد مانند قرار ملاقات با پزشک.\n"
"بازه‌ی زمانی یک باره تنها زمانی مورد استفاده قرار می‌گیرد که کاربر با تعیین یک بازه‌ی زمانی غیرتکراری از تقویم (به طورمثال 10 آوریل 2021 از ساعت 10 تا 11 صبح) قرار ملاقات اختصاصی را برای مشتری ایجاد کند."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__event_videocall_source
msgid ""
"Defines the type of video call link that will be used for the generated "
"events. Keep it empty to prevent generating meeting url."
msgstr ""
"نوع لینک تماس تصویری که برای رویدادهای ایجاد شده مورد استفاده قرار می‌گیرد "
"را مشخص می‌کند. برای جلوگیری از ایجاد نشانی جلسه آن را خالی نگه دارید."

#. module: appointment
#: model:appointment.type,name:appointment.appointment_type_dental_care
msgid "Dental Care"
msgstr "خدمات دندان‌پزشکی"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__description
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "Description"
msgstr "توصیف"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__destination_resource_ids
msgid "Destination combination"
msgstr "ترکیب مقصد"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Details"
msgstr "جزییات"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__allday
msgid ""
"Determine if the slot englobe the whole day, mainly used for unique slot "
"type"
msgstr ""
"مشخص کنید که آیا بازه‌ی زمانی کل روز را در بر می‌گیرد یا نه، عمدتاًٌ برای یک"
" بازه‌ی زمانی انحصاری مورد استفاده قرار می‌گیرد"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form_insert_link
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
#, python-format
msgid "Discard"
msgstr "رها کردن"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_question__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_type__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__avatars_display
msgid "Display the Users'/Resources' picture on the Website."
msgstr "تصویر کاربران/منابع را روی وب‌سایت نمایش دهید."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__resource_manual_confirmation
msgid ""
"Do not automatically accept meetings created from the appointment once the total capacity\n"
"            reserved for a slot exceeds the percentage chosen. The appointment is still considered as reserved for\n"
"            the slots availability."
msgstr ""
"هنگامی که کل ظرفیت رزرو شده برای یک بازه‌ی زمانی از درصد انتخاب شده فراتر "
"رفت، از پذیرش خودکار جلسات ایجاد شده از قرار ملاقات‌ها خودداری کنید. قرار "
"ملاقات هنوز برای بازه‌های زمانی موجود رزرو تلقی می‌شود."

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__select
msgid "Dropdown (one answer)"
msgstr "دراپ داون (یک پاسخ)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__duration
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_duration
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Duration"
msgstr "مدت زمان"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Email*"
msgstr "ایمیل"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Email: %(email)s"
msgstr "ایمیل: %(email)s"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/appointment.py:0
#, python-format
msgid "Email: %s"
msgstr "ایمیل: %s"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__leave_end_dt
msgid "End Date"
msgstr "تاریخ پایان"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__end_datetime
msgid "End Datetime"
msgstr "تاریخ و زمان پایان"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__end_datetime
msgid "End datetime for unique slot type management"
msgstr "تاریخ و زمان پایان برای مدیریت بازه‌های زمانی انحصاری"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__end_hour
msgid "Ending Hour"
msgstr "ساعت پایان"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_alarm
msgid "Event Alarm"
msgstr "اطلاع رسانی رویداد"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "Event Details"
msgstr "جزئیات رویداد"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Every"
msgstr "هر"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Extra Comments..."
msgstr "توضیحات بیشتر"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Extra Message on Confirmation"
msgstr "پیام دیگری برای تأیید "

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_confirmation
msgid "Extra information provided once the appointment is booked."
msgstr "هنگامی که قرار ملاقات رزرو شد، اطلاعات بیشتری ارائه می‌شود. "

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_appointment
msgid "Follow, reschedule or cancel your appointments"
msgstr "قرار ملاقات‌های خود را پیگیری یا لغو کرده و یا مجدد برنامه‌ریزی کنید"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_follower_ids
msgid "Followers"
msgstr "دنبال کنندگان"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "For"
msgstr "برای"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__5
msgid "Friday"
msgstr "جمعه"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "From"
msgstr "از"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__avatars_display
msgid "Front-End Display"
msgstr "نمایش Front-End"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Full name*"
msgstr "نام کامل"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Get Share Link"
msgstr "دریافت لینک اشتراک‌گذاری"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#: model_terms:ir.ui.view,arch_db:appointment.appointment_onboarding_link_view_form
#, python-format
msgid "Get Your Link"
msgstr "لینک خود را انتخاب کنید"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_onboarding_link
msgid "Get a link to an appointment type during the onboarding"
msgstr "طی ثبت یک قرار ملاقات لینک منتهی به آن را انتخاب کنید"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_invite__suggested_staff_user_ids
msgid ""
"Get the users linked to the appointment type selected to apply a domain on "
"the users that can be selected"
msgstr ""
"برای تعیین دامنه‌ی انتخاب کاربران، آنها را به یک لینک به نوع قرار ملاقات "
"مشخص شده ربط دهید"

#. module: appointment
#: model:onboarding.onboarding.step,title:appointment.appointment_onboarding_preview_invite_step
msgid "Get your link"
msgstr "لینک خود را انتخاب کنید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Google Agenda"
msgstr "دستور کار گوگل"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Group By"
msgstr "گروه‌بندی برمبنای"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Guest usage is limited to 10 customers for performance reason."
msgstr "به منظور بهبود عملکرد، کاربری مهمان محدود به 10 مشتری است."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Guests"
msgstr "مهمانان"

#. module: appointment
#: model:ir.model,name:appointment.model_ir_http
msgid "HTTP Routing"
msgstr "مسیریابی HTTP"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__has_message
msgid "Has Message"
msgstr "آیا دارای پیام است"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "How to join"
msgstr "نحوه‌ی عضویت"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__assign_method
msgid ""
"How users and resources will be assigned to meetings customers book on your "
"website."
msgstr ""
"چگونه می‌توان کاربران و منابع را به دفتر ثبت مشتریان و جلسات موجود در وب "
"سایت اختصاص داد."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__id
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__id
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__id
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__id
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__id
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__id
#: model:ir.model.fields,field_description:appointment.field_appointment_question__id
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__id
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__id
#: model:ir.model.fields,field_description:appointment.field_appointment_type__id
msgid "ID"
msgstr "شناسه"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"اگر این گزینه را انتخاب کنید، پیام‌های جدید به توجه شما نیاز خواهند داشت."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_has_error
#: model:ir.model.fields,help:appointment.field_appointment_type__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "اگر علامت زده شود، برخی از پیام ها دارای خطای تحویل هستند."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "If empty, Odoo will not send emails"
msgstr "در صورت خالی بودن، اودوو ایمیلی را ارسال نمی‌کند"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__restrict_to_resource_ids
msgid ""
"If empty, all resources are considered to be available.\n"
"If set, only the selected resources will be taken into account for this slot."
msgstr ""
"خالی بودن این فیلد نشان‌دهنده‌ی موجود بودن تمام منابع است. \n"
"در صورت تنظیم این فیلد، تنها منابع انتخاب شده برای این بخش به حساب می‌آیند."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__restrict_to_user_ids
msgid ""
"If empty, all users are considered to be available.\n"
"If set, only the selected users will be taken into account for this slot."
msgstr ""
"خالی بودن این فیلد نشان‌دهنده‌ی قابل دسترس بودن تمام کاربران است. \n"
"در صورت تنظیم، تنها کاربران انتخاب شده برای این بخش به حساب می‎آیند."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "If empty, the meeting is considered as taking place online"
msgstr "خالی بودن این فیلد نشان‌دهنده‌ی برگزاری جلسه به صورت آنلاین است"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__canceled_mail_template_id
msgid ""
"If set an email will be sent to the customer when the appointment is "
"canceled."
msgstr ""
"در صورت تنظیم این فیلد، به محض لغو شدن یک قرار ملاقات، ایمیلی به مشتری ارسال"
" می‌شود."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__booked_mail_template_id
msgid ""
"If set an email will be sent to the customer when the appointment is "
"confirmed."
msgstr ""
"در صورت تنظیم این فیلد، به محض تأیید شدن قرار ملاقات، یک ایمیل به مشتری "
"ارسال می‌شود."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"اگر فیلد فعال را روی خیر قرار دهید، رکورد منبع را بدون اینکه حذف کنید مخفی "
"خواهید کرد."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"اگر فیلد فعال روی نادرست تعیین شود، به شما اجازه می‌دهد هشدار رویداد را "
"پنهان کنید بودن اینکه آن را حذف کنید."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_1920
msgid "Image"
msgstr "تصویر"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_1024
msgid "Image 1024"
msgstr "تصویر 1024"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_128
msgid "Image 128"
msgstr "تصویر 128"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_256
msgid "Image 256"
msgstr "تصویر 256"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_512
msgid "Image 512"
msgstr "تصویر 512"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Insert Appointment Link"
msgstr "لینک قرار ملاقات را وارد کنید."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form_insert_link
msgid "Insert link"
msgstr "لینک را وارد کنید"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_intro
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Introduction Message"
msgstr "پیام معرفی"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/appointment_form.js:0
#: code:addons/appointment/static/src/js/appointment_validation.js:0
#, python-format
msgid "Invalid Email"
msgstr "ایمیل نامعتبر"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_invite_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_tree
msgid "Invitation Links"
msgstr "لینک‌های دعوت"

#. module: appointment
#: model:mail.template,description:appointment.attendee_invitation_mail_template
msgid "Invitation email to new attendees of an appointment"
msgstr "ارسال ایمیل دعوت به شرکت‌کنندگان جدید یک قرار ملاقات"

#. module: appointment
#: model:mail.template,subject:appointment.attendee_invitation_mail_template
msgid "Invitation to {{ object.event_id.name }}"
msgstr "ارسال دعوت‌نامه به {{object.event_id.name}}"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_type_action_custom
#: model:ir.ui.menu,name:appointment.menu_appointment_type_custom
msgid "Invitations"
msgstr "دعوت‌نامه‌ها"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_is_follower
msgid "Is Follower"
msgstr "آیا دنبال می کند"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__is_published
msgid "Is Published"
msgstr "منتشر شده است"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"It's too late to cancel online, please contact the attendees another way if "
"you really can't make it."
msgstr ""
"برای لغو قرار ملاقات به صورت آنلاین دیگر دیر شده است، در این صورت با روش "
"دیگری با شرکت‌کننده تماس بگیرید."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Join at"
msgstr "عضو شدن در"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Join with Odoo Discuss"
msgstr "به بحث اودوو ملحق شوید"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__country_ids
msgid ""
"Keep empty to allow visitors from any country, otherwise you only allow "
"visitors from selected countries"
msgstr ""
"این فیلد را برای امکان بازدید افراد از سراسر جهان خالی نگه دارید، در غیر این"
" صورت تنها بازدیدکنندگان چند کشور منتخب مجاز هستند."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_question__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_type__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_question__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_type__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__allow_guests
msgid "Let attendees invite guests when registering a meeting."
msgstr "شرکت‌کنندگان می‌توانند هنگام ثبت یک جلسه، مهمانانی را دعوت کنند. "

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Link Copied in your clipboard!"
msgstr "لینک‌های کپی شده در کلیپ‌بورد"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "Link Generator"
msgstr "لینک‌ساز"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__book_url
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "Link URL"
msgstr "نشانی لینک"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/components/appointment_invite/appointment_invite_copy_close.js:0
#: code:addons/appointment/static/src/components/appointment_onboarding/appointment_onboarding_invite_buttons.js:0
#, python-format
msgid "Link copied to clipboard!"
msgstr "لینک در کلیپ‌ بورد کپی شد!"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__linked_resource_ids
msgid "Linked Resource"
msgstr "منبع مرتبط"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__linked_resource_ids
msgid "List of resources that can be combined to handle a bigger demand."
msgstr ""
"فهرستی از منابع که می‌توانند برای رسیدگی به یک تقاضای بزرگ‌تر با هم ترکیب "
"شوند."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__location_id
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Location"
msgstr "مکان"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__location
msgid "Location formatted"
msgstr "مکان قالب‌بندی شد"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__location
msgid "Location formatted for one line uses"
msgstr "مکان برای کاربردهای تک سطر ی قالب‎‌بندی شد"

#. module: appointment
#: model:onboarding.onboarding.step,done_text:appointment.appointment_onboarding_create_appointment_type_step
msgid "Looks great!"
msgstr "عالی به نظر میاد!"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_resources.xml:0
#, python-format
msgid "Make your choice"
msgstr "گزینه‌ی موردنظر خود را انتخاب کنید"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_manage_capacity
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_manage_capacity
msgid "Manage Capacities"
msgstr "مدیریت ظرفیت‌ها"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__resource_manage_capacity
#: model:ir.model.fields,help:appointment.field_calendar_event__appointment_type_manage_capacity
msgid ""
"Manage the maximum amount of people a resource can handle (e.g. Table for 6 "
"persons, ...)"
msgstr ""
"حداکثر افرادی یک منبع قادر به کنترل آنها است را مدیریت کنید (به طورمثال میز "
"6 نفره و ...)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_manual_confirmation
msgid "Manual Confirmation"
msgstr "تأیید دستی"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Max in"
msgstr "حداکثر"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__capacity
msgid ""
"Maximum amount of people for this resource (e.g. Table for 6 persons, ...)"
msgstr "حداکثر تعداد افراد این منبع (به طورمثال میز 6 نفره و ...)"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_invite__resources_choice__current_user
msgid "Me (only with Users)"
msgstr "من (تنها برای کاربران)"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Meet With Me"
msgstr "با من ملاقات کن"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__videocall_redirection
msgid "Meeting redirection URL"
msgstr "نشانی جدید جلسه"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "Meetings"
msgstr "جلسات"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_has_error
msgid "Message Delivery error"
msgstr "خطای تحویل پیام"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Message from the Organizer"
msgstr "پیامی از طرف سازمان‌دهنده‌ی جلسه"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Messages"
msgstr "پیام ها"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Min"
msgstr "حداقل"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__1
msgid "Monday"
msgstr "دوشنبه"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "More Options"
msgstr "گزینه‌های بیشتر"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__text
msgid "Multi-line text"
msgstr "متن چند سطری"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "My Appointments"
msgstr "قرار ملاقات‌های من"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_search
msgid "My Links"
msgstr "لینک‌های من"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr "نام"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Navigation"
msgstr "هدایت"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_alarm__default_for_new_appointment_type
msgid "New Appointments Default"
msgstr "پیشفرض قرار ملاقات جدید"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_answer_input_action_from_question
msgid "No Answers yet!"
msgstr "هنوز هیچ پاسخی وجود ندارد!"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action
msgid "No Appointment Configured"
msgstr "هیچ قرار ملاقاتی شکل نگرفت"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_view_bookings_resources
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_view_bookings_users
msgid "No Appointment or Resource were found."
msgstr "هیچ قرار ملاقات یا منبعی یافت نشد."

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action_custom
msgid "No Custom Availabilities Shared!"
msgstr "هیچ امکانات سفارشی‌سازی شده‌‎ای یافت نشد!"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__avatars_display__hide
msgid "No Picture"
msgstr "هیچ تصویری وجود ندارد"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_invite_action
msgid "No Shared Links yet!"
msgstr "هنوز لینکی به اشتراک گذاشته نشده است!"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__appointment_type_info_msg
msgid "No User Assigned Message"
msgstr "هیچ کاربری برای پیام مشخص نشده است"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_appointment_reporting
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report_all
msgid "No data yet!"
msgstr "هنوز هیچ داده ای وجود ندارد!"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "هیچکدام"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_event__alarm_ids
msgid "Notifications sent to all attendees to remind of the meeting."
msgstr "اعلان ها برای یادآوری جلسه برای همه شرکت کنندگان ارسال شد."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_needaction_counter
msgid "Number of Actions"
msgstr "تعداد اقدامات"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_has_error_counter
msgid "Number of errors"
msgstr "تعداد خطاها"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "تعداد پیام‌هایی که نیاز به اقدام دارند"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "تعداد پیام‌های با خطای تحویل"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Number of people"
msgstr "تعداد افراد"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__event_videocall_source__discuss
msgid "Odoo Discuss"
msgstr "بحث اودوو"

#. module: appointment
#: model:ir.model,name:appointment.model_onboarding_onboarding
msgid "Onboarding"
msgstr "معارفه سازمانی"

#. module: appointment
#: model:onboarding.onboarding.step,step_image_alt:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "Onboarding Connect your calendar"
msgstr "در حال پذیرش، به تقویم خود متصل شوید"

#. module: appointment
#: model:onboarding.onboarding.step,step_image_alt:appointment.appointment_onboarding_preview_invite_step
msgid "Onboarding Get your link"
msgstr "در حال پذیرش، لینک خود را انتخاب کنید"

#. module: appointment
#: model:onboarding.onboarding.step,step_image_alt:appointment.appointment_onboarding_create_appointment_type_step
msgid "Onboarding Set Your Availabilities"
msgstr "در حال پذیرش، امکانات خود را تعیین کنید"

#. module: appointment
#: model:ir.model,name:appointment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "مراحل معارفه سازمانی"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__slot_type__unique
msgid "One Shot"
msgstr "یک باره"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid ""
"Only letters, numbers, underscores and dashes are allowed in your links."
msgstr "فقط حروف، اعداد، زیرنویس‌ها و خطوط فاصله در لینک‌ها مجاز هستند."

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_invite.py:0
#, python-format
msgid ""
"Only letters, numbers, underscores and dashes are allowed in your links. You"
" need to adapt %s."
msgstr ""
"فقط حروف، اعداد، زیرنویس‌ها و خطوط فاصله در لینک‌ها مجاز هستند. باید این "
"قواعد را رعایت کنید %s. "

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Only one anytime appointment type is allowed for a specific user."
msgstr "فقط قرار ملاقاتی که زمان مشخصی ندارد برای یک کاربر خاص مجاز است."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Open"
msgstr "باز"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "Opening Hours"
msgstr "ساعات آغازین"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
msgid "Operator"
msgstr "اپراتور"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Options"
msgstr "گزینه‌ها"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__user_id
msgid "Organizer"
msgstr "سازمان‌دهنده"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Our first availability is"
msgstr "امکان اول ما عبارت است از"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Outlook"
msgstr "Outlook"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Past"
msgstr "گذشته"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_booker_id
msgid "Person who is booking the appointment"
msgstr "فردی که قرار ملاقات را ثبت می‌کند"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Phone number*"
msgstr "شماره تلفن"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Phone: %(phone)s"
msgstr "تلفن: %(phone)s"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/appointment.py:0
#, python-format
msgid "Phone: %s"
msgstr "تلفن: %s"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__assign_method__resource_time
msgid "Pick User/Resource then Time"
msgstr "ابتدا کاربر/منبع و سپس زمان را انتخاب کنید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Pick a Work Schedule..."
msgstr "یک زمان‌بندی را برای کار انتخاب کنید ..."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "Pick a schedule to restrict openings"
msgstr "یک زمان‌بندی را برای محدود کردن زمان شروع انتخاب کنید"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Pick your availabilities"
msgstr "امکانات خود را انتخاب کنید"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__placeholder
msgid "Placeholder"
msgstr "مکان‌نما"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Please, select another date."
msgstr "لطفا تاریخ دیگری انتخاب کنید."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_resource_ids
msgid "Possible resources"
msgstr "منابع احتمالی"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_staff_user_ids
msgid "Possible users"
msgstr "کاربران احتمالی"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/components/appointment_onboarding/appointment_onboarding_invite_buttons.xml:0
#: model:onboarding.onboarding.step,button_text:appointment.appointment_onboarding_preview_invite_step
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#, python-format
msgid "Preview"
msgstr "پیش‌نمایش"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__punctual
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Punctual"
msgstr "دقیق"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__question_id
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__question_id
#: model:ir.model.fields,field_description:appointment.field_appointment_question__name
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Question"
msgstr "پرسش"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__question_type
#: model:ir.model.fields,field_description:appointment.field_appointment_question__question_type
msgid "Question Type"
msgstr "نوع سوال"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__question_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Questions"
msgstr "پرسش‌ها"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__radio
msgid "Radio (one answer)"
msgstr "رادیو (یک پاسخه)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__rating_ids
msgid "Ratings"
msgstr "رتبه‌ها"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__reason
msgid "Reason"
msgstr "علت"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__slot_type__recurring
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__recurring
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Recurring"
msgstr "تکراری"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__redirect_url
msgid "Redirect URL"
msgstr "نشانی جدید"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__reminder_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_event__alarm_ids
#: model:ir.ui.menu,name:appointment.menu_appointment_reminders
msgid "Reminders"
msgstr "یادآورها"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Remove"
msgstr "حذف"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_schedule_report
#: model:ir.ui.menu,name:appointment.reporting_menu_calendar
msgid "Reporting"
msgstr "گزارش"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__question_required
msgid "Required Answer"
msgstr "پاسخ موردنیاز"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__resource_id
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Resource"
msgstr "منبع"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_view_bookings_resources
#: model:ir.actions.server,name:appointment.calendar_event_action_all_resources_bookings
msgid "Resource Bookings"
msgstr "رزرو منابع"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__calendar_id
msgid "Resource Calendar"
msgstr "تقویم منبع"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_appointment_resource_leaves
msgid "Resource Leaves"
msgstr "مرخصی‌های منابع انسانی"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_resource_action
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__resource_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resource_ids
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__schedule_based_on__resources
#: model:ir.ui.menu,name:appointment.menu_appointment_resource
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree_invitation
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "Resources"
msgstr "منابع"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_appointment_schedule_resource_booking
msgid "Resources Bookings"
msgstr "رزرو منابع "

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resources_on_leave
msgid "Resources intersecting with leave time"
msgstr "منابعی که با زمان مرخصی‌ تداخل دارند"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Responsible"
msgstr "پاسخگو"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_tree
msgid "Restrict to Resource"
msgstr "محدود به منبع"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__restrict_to_resource_ids
msgid "Restrict to Resources"
msgstr "محدود به منابع"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_tree
msgid "Restrict to User"
msgstr "محدود به کاربر"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__restrict_to_user_ids
msgid "Restrict to Users"
msgstr "محدود به کاربران"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Restrict to specific Resources"
msgstr "محدود به منابع خاص"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "SCHEDULED"
msgstr "بر نامه‌ریزی شده"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطای تحویل پیامک"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__6
msgid "Saturday"
msgstr "شنبه"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Save"
msgstr "ذخیره"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_appointment_schedule_resources
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Schedule"
msgstr "زمان‌بندی"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__schedule_based_on
msgid "Schedule Based On"
msgstr "برنامه‌ریزی براساس"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Schedule an Appointment"
msgstr "برنامه‌ریزی یک قرار ملاقات"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_appointment_reporting
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report_all
msgid "Schedule appointments to get statistics"
msgstr "برنامه‌ریزی یک قرار ملاقات برای گرفتن آمار"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__min_schedule_hours
msgid "Schedule before (hours)"
msgstr "برنامه‌ریزی قبل از (ساعت)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__max_schedule_days
msgid "Schedule not after (days)"
msgstr "پس از (روز) برنامه‌ریزی نشود"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Scheduling"
msgstr "برنامه‌ریزی"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "جستجو در همه"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr "جستجو در توضیحات"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Name"
msgstr "جستجو با نام"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Responsible"
msgstr "جستجو براساس مسئولیت"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Select Dates"
msgstr "انتخاب تاریخ‌ها"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__assign_method__time_resource
msgid "Select Time then User/Resource"
msgstr "انتخاب زمان و سپس کاربر/منابع"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__assign_method__time_auto_assign
msgid "Select Time then auto-assign"
msgstr "انتخاب زمان و تاریخ و سپس انتصاب خودکار"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Select a date &amp; time"
msgstr "انتخاب زمان و تاریخ"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_slots.xml:0
#, python-format
msgid "Select a time"
msgstr "زمانی را انتخاب کنید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Select attendees..."
msgstr "انتخاب حاضرین..."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__value_answer_id
msgid "Selected Answer"
msgstr "پاسخ‌های انتخاب شده"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__appointment_type_count
msgid "Selected Appointments Count"
msgstr "تعداد قرار ملاقات‌های انتخاب شده"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Selection Questions"
msgstr "انتخاب پرسش‌ها"

#. module: appointment
#: model:mail.template,description:appointment.appointment_canceled_mail_template
msgid "Sent to all attendees when an appointment is cancelled"
msgstr "در صورت لغو قرار ملاقات، برای تمام شرکت‌کنندگان آن ارسال شود"

#. module: appointment
#: model:mail.template,description:appointment.appointment_booked_mail_template
msgid "Sent to followers of an appointment type when a meeting is booked"
msgstr "پس از رزرو جلسه، پیامی برای اعضای آن ارسال شده است"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__sequence
#: model:ir.model.fields,field_description:appointment.field_appointment_question__sequence
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__sequence
#: model:ir.model.fields,field_description:appointment.field_appointment_type__sequence
msgid "Sequence"
msgstr "دنباله"

#. module: appointment
#: model:onboarding.onboarding.step,title:appointment.appointment_onboarding_create_appointment_type_step
msgid "Set your availabilities"
msgstr "امکانات خود را مشخص کنید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree
msgid "Share"
msgstr "اشتراک‌گذاری"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Share Appointment Link"
msgstr "لینک قرار ملاقات را به اشتراک بگذارید"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Share Availabilities"
msgstr "به اشتراک‌گذاشتن ساعات در دسترس"

#. module: appointment
#. odoo-javascript
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.js:0
#, python-format
msgid "Share Link"
msgstr "اشتراک لینک"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_invite_action
#: model:ir.ui.menu,name:appointment.menu_appointment_invite
msgid "Share Links"
msgstr "اشتراک گذاری لینک‌ها"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action
msgid ""
"Share calendar link allowing people to book meetings with you, your team or "
"a resource."
msgstr ""
"لینک گاهشمار که به افراد اجازه می‌دهد جلساتی را با شما، منابع یا تیم‌تان "
"رزرو کنند، به اشتراک بگذارید."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_onboarding_link_view_form
msgid ""
"Share this link to let people book meetings with you, your team or a "
"resource."
msgstr ""
"این لینک را به اشتراک بگذارید تا افراد بتوانند جلسات خود را با شما، منابع یا"
" تیم‌تان به اشتراک بگذارند"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__shareable
msgid "Shareable"
msgstr "قابل اشتراک"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Shared Links"
msgstr "لینک‌های به اشتراک گذاشته شده"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__short_code
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__short_code
msgid "Short Code"
msgstr "کد کوتاه"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__short_code_format_warning
msgid "Short Code Format Warning"
msgstr "هشدار در مورد قالب کد کوتاه"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__short_code_unique_warning
msgid "Short Code Unique Warning"
msgstr "هشدار منحصر به کد کوتاه"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__avatars_display__show
msgid "Show Pictures"
msgstr "نمایش تصاویر"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__char
msgid "Single line text"
msgstr "متن تک سطر ی"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__slot_type
msgid "Slot type"
msgstr "نوع بازه‌ی زمانی"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_intro
msgid "Small description of the appointment type."
msgstr "شرح مختصری از نوع قرار ملاقات"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry,"
msgstr "متاسفیم،"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, it is no longer possible to schedule an appointment."
msgstr "متأسفیم، دیگر امکان برنامه‌ریزی یک قرار ملاقات وجود ندارد."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, there is not any more availability for the asked capacity."
msgstr "متأسفیم، ظرفیت مورد درخواست دیگر وجود ندارد."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, we have no availability for an appointment."
msgstr "متأسفیم، دیگر امکانی برای رزرو قرار ملاقات وجود ندارد."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, we have no more slots available for this month."
msgstr "متأسفیم، هیچ بازه‌ی زمانی برای قرار ملاقات در این ماه وجود ندارد."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__source_resource_ids
msgid "Source combination"
msgstr "ترکیب منابع"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__appointment_resource_ids
msgid "Specific Resources"
msgstr "منابع خاص"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_invite__resources_choice__specific_resources
msgid "Specific Users/Resources"
msgstr "کاربران/منابع خاص"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_view_bookings_users
#: model:ir.actions.server,name:appointment.calendar_event_action_all_users_appointments
#: model:ir.ui.menu,name:appointment.menu_appointment_schedule_staff_appointment
msgid "Staff Bookings"
msgstr "رزرو کارکنان"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__leave_start_dt
msgid "Start Date"
msgstr "تاریخ آغاز"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__start_datetime
msgid "Start Datetime"
msgstr "تاریخ و ساعت شروع"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__event_start
msgid "Start date of an event, without time for full days events"
msgstr "تاریخ شروع یک رویداد، بدون زمان برای رویدادهای روز کامل"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__start_datetime
msgid "Start datetime for unique slot type management"
msgstr "تاریخ و ساعت شروع برای "

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__start_hour
msgid "Starting Hour"
msgstr "ساعت شروع"

#. module: appointment
#: model:onboarding.onboarding.step,done_text:appointment.appointment_onboarding_configure_calendar_provider_step
#: model:onboarding.onboarding.step,done_text:appointment.appointment_onboarding_preview_invite_step
msgid "Step Completed!"
msgstr "گام کامل شد!"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__event_stop
msgid "Stop date of an event, without time for full days events"
msgstr "تاریخ پایان برای یک رویداد، بدون زمان برای رویدادهای روز کامل"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Subject"
msgstr "موضوع"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__answer_input_ids
msgid "Submitted Answers"
msgstr "پاسخ‌های پذیرفته شده"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__7
msgid "Sunday"
msgstr "1‌شنبه"

#. module: appointment
#: model:appointment.question,name:appointment.appointment_type_dental_care_question_1
msgid "Symptoms"
msgstr "نشانه‌ها"

#. module: appointment
#: model:appointment.type,name:appointment.appointment_type_tennis_court
msgid "Tennis Court"
msgstr "زمین تنیس"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__value_text_box
msgid "Text Answer"
msgstr "پاسخ مکتوب"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Text Questions"
msgstr "پرسش‌های مکتوب"

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_invite_short_code_uniq
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "The URL is already taken, please pick another code."
msgstr "نشانی قبلاً انتخاب شده است، لطفاً کد دیگری را انتخاب کنید."

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_type_check_resource_manual_confirmation_percentage
msgid "The capacity percentage should be between 0 and 100%"
msgstr "درصد پوشش‌دهی باید بین 0 و 100 باشد"

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_booking_line_check_capacity_reserved
msgid "The capacity reserved should be positive."
msgstr "ظرفیت رزرو شده باشد مثبت باشد."

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_booking_line_check_capacity_used
msgid "The capacity used can not be lesser than the capacity reserved"
msgstr "ظرفیت مورد استفاده نباید کمتر از ظرفیت رزرو شده باشد"

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_slot_check_start_and_end_hour
msgid "The end time must be later than the start time."
msgstr "زمان پایان باید بعد از زمان شروع باشد."

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "The event %s cannot book resources without an appointment type."
msgstr ""
"امکان رزرو منابع برای رویداد %s بدون تعیین نوع قرار ملاقات وجود ندارد."

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "The field '%s' does not exist in the targeted model"
msgstr "فیلد %s در مدل موردنظر وجود ندارد"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_invite.py:0
#, python-format
msgid "The following appointment type(s) have no resource assigned: %s."
msgstr "هیچ منبعی برای قرار ملاقات‌(های) زیر مشخص نشده است: %s."

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_invite.py:0
#, python-format
msgid "The following appointment type(s) have no staff assigned: %s."
msgstr "هیچ کارمندی برای قرار ملاقات(های) زیر مشخص نشده است: %s."

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_question.py:0
#, python-format
msgid "The following question(s) do not have any selectable answers : %s"
msgstr "پرسش‌(های) زیر هیچ پاسخ مناسبی ندارند: %s"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid ""
"The following users are in restricted slots but they are not part of the "
"available staff: %s"
msgstr ""
"کاربران زیر در بازه‌های زمانی محدود هستند اما بخشی از کارکنان موجود نیستند: "
"%s"

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_resource_check_capacity
msgid "The resource should have at least one capacity."
msgstr "منبع حداقل باید یک ظرفیت داشته باشد."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__sequence
msgid ""
"The sequence dictates if the resource is going to be picked in higher priority against another resource\n"
"        (e.g. for 2 tables of 4, the lowest sequence will be picked first)"
msgstr ""
"توالی نشان می‌دهد که آیا این منبع نسبت به منابع دیگر با اولویت بیشتری انتخاب می‌شود یا نه\n"
"(به طورمثال، برای 2 جدول از میان 4 جدول، ابتدا پایین‌ترین توالی انتخاب می‌شود)"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Their first availability is"
msgstr "اولین امکان عبارت است از"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "There is currently no appointment available"
msgstr "در حال حاضر هیچ قرار ملاقاتی امکان‌پذیر نیست"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "There is no appointment linked to your account."
msgstr "هیچ قرار ملاقاتی با حساب شما مرتبط نیست."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__shareable
msgid ""
"This allows to share the resource with multiple attendee for a same time "
"slot (e.g. a bar counter)"
msgstr ""
"این کار امکان به اشتراک‌گذاری منبع با چند شرکت‌کننده را در یک بازه‌ی زمانی "
"فراهم می‌کند (مانند یک پیشخوان)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it does not have any "
"opening hours configured"
msgstr ""
"این قرار ملاقات امکان‌پذیر نیست زیرا هیچ زمانی برای شروع آن مشخص نشده است"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no resource "
"assigned"
msgstr "این قرار ملاقات امکان‌پذیر نیست زیرا هیچ منبعی برای آن تعیین نشده است"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no resource "
"assigned and does not have any opening hours configured"
msgstr ""
"این قرار ملاقات به هیچ وجه امکان‌پذیر نیست زیرا هیچ منبعی به آن اختصاص داده "
"نشده و ساعت شروع آن مشخص نیست"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no staff assigned"
msgstr ""
"این قرار ملاقات به هیچ وجه امکان‌پذیر نیست زیرا هیچ کارمندی به آن اختصاص "
"داده نشده"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no staff assigned"
" and does not have any opening hours configured"
msgstr ""
"این قرار ملاقات به هیچ وجه امکان‌پذیر نیست زیرا هیچ کارمندی به آن اختصاص "
"داده نشده و ساعت شروع ان مشخص نیست"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"این فیلد به منظور تعیین اینکه منابع در کدام منطقه زمانی کار می کنند استفاده "
"می شود."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_edit_in_backend
msgid "This is a preview of the customer appointment form."
msgstr "این پیش نمایشی از فرم قرار ملاقات مشتری است."

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__4
msgid "Thursday"
msgstr "5شنبه"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__tz
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_tz
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Timezone"
msgstr "منطقه‌ زمانی"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__appointment_tz
msgid "Timezone where appointment take place"
msgstr "منطقه‌ی زمانی که قرار ملاقات در آن اتفاق می‌افتد"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Timezone:"
msgstr "منطقه زمانی:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "To"
msgstr "تا"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__access_token
msgid "Token"
msgstr "توکن"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "Total"
msgstr "مجموع"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_total_capacity
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_tree
msgid "Total Capacity"
msgstr "ظرفیت کل"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resource_total_capacity_reserved
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Total Capacity Reserved"
msgstr "کل ظرفیت رزرو شده"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resource_total_capacity_used
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Total Capacity Used"
msgstr "کل ظرفیت استفاده شده"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Total Reserved"
msgstr "کل ظرفیت رزرو شده"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Total:"
msgstr "مجموع:"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__2
msgid "Tuesday"
msgstr "3‌شنبه"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Type"
msgstr "نوع"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Uncertain"
msgstr "نامشخص"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/gantt/gantt_popover.xml:0
#, python-format
msgid "Unconfirm Check-In"
msgstr "عدم تأیید پذیرش"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Until"
msgstr "تا"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Until (max)"
msgstr "تا (حداکثر)"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Upcoming"
msgstr "پیش رو"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_alarm__default_for_new_appointment_type
msgid "Use as default for new Appointment Types"
msgstr "به عنوان پیشفرض برای انواع قرار ملاقات استفاده کنید"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__category
msgid ""
"Used to define this appointment type's category.\n"
"\n"
"        Can be one of:\n"
"\n"
"            - Recurring: the default category, weekly recurring slots. Accessible from the website\n"
"\n"
"            - Punctual: recurring slots limited between 2 datetimes. Accessible from the website\n"
"\n"
"            - Custom: the user will create and share to another user a custom appointment type with hand-picked time slots\n"
"\n"
"            - Anytime: the user will create and share to another user an appointment type covering all their time slots"
msgstr ""
"برای تعیین دسته‌بندی این نوع قرار ملاقات مورد استفاده قرار می‌گیرد\n"
"می‌تواند یکی از این موارد باشد:\n"
"- تکرار شونده: دسته‌بندی پیشفرض، بازه‌های زمانی که هر هفته تکرار می‌شوند\n"
"از وب سایت قابل دسترسی هستند\n"
"- دقیق: بازه‌های زمانی تکرار شونده که محدود به 2 تاریخ و زمان مشخص هستند \n"
"از وب سایت قابل دسترسی هستند\n"
"- اختصاصی: کاربر یک قرار ملاقات را به صورت دستی ایجاد کرده و آن را با کاربر دیگری به اشتراک می‌گذارد\n"
"- هر زمان ممکن: کاربر یک قرار ملاقات که شامل تمام بازه‌های زمانی است را ایجاد کرده و آن را با کاربر دیگری به اشتراک می‌گذارد"

#. module: appointment
#: model:res.groups,name:appointment.group_appointment_user
msgid "User"
msgstr "کاربر"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__staff_user_ids
#: model:ir.model.fields,field_description:appointment.field_appointment_type__staff_user_ids
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__schedule_based_on__users
msgid "Users"
msgstr "کاربران"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__event_videocall_source
msgid "Videoconference Link"
msgstr "لینک ویدئو کنفرانس"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__website_message_ids
msgid "Website Messages"
msgstr "پیام‌های وب‌سایت"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__website_message_ids
msgid "Website communication history"
msgstr "تاریخچه ارتباط با وب‌سایت"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__3
msgid "Wednesday"
msgstr "4شنبه"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__weekday
msgid "Week Day"
msgstr "روز هفته"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__resource_calendar_id
msgid ""
"If kept empty, the working schedule of the company set on the resource will "
"be used"
msgstr ""
"اگر خالی نگه داشته شود، از جدول زمان‌بندی کار شرکت تنظیم شده براساس منابع "
"استفاده می‌شود"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.staff_user_select
msgid "With"
msgstr "با"

#. module: appointment
#: model:onboarding.onboarding.step,description:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "With Outlook or Google"
msgstr "با Outlook یا گوگل"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__resource_calendar_id
msgid "Working Hours"
msgstr "ساعت‌های کاری"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/appointment_form.js:0
#: code:addons/appointment/static/src/js/appointment_validation.js:0
#, python-format
msgid "You cannot invite more than 10 people"
msgstr "نمی‌توانید بیش از 10 نفر را دعوت کنید"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_details_column
msgid "Your Appointment"
msgstr "قرار ملاقات شما"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"Your appointment has been reserved! We will come back to you to confirm it."
msgstr "قرار ملاقات شما لغو شده است! برای تأیید آن به شما مراجعه خواهیم کرد."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Your appointment has successfully been booked!"
msgstr "قرار ملاقات شما با موفقیت رزرو شده است!"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Your appointment is in less than"
msgstr "قرار ملاقات شما از ... کمتر است"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
msgid "Your choice"
msgstr "انتخاب شما"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "days"
msgstr "روز"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"During this meeting, we will...\""
msgstr "به طور مثال «طی جلسه، ما ...»"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"I feel nauseous...\""
msgstr "به طورمثال «حالت تهوع دارم ...»"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "e.g. \"John Doe - Tennis Court Booking\""
msgstr "به طورمثال «جان دو - رزرو زمین تنیس»"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"Technical Demo\""
msgstr "به طورمثال «دموی فنی»"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"Thank you for your trust, we look forward to meeting you!\""
msgstr "به طورمثال «از اعتماد شما متشکریم، منتظر ملاقات با شما هستیم!»"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"What are your symptoms?\""
msgstr "به طور مثال «علائم شما چیست؟»"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "e.g. +1(605)691-3277"
msgstr "e.g. +1(605)691-3277"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "e.g. Inventory count and valuation"
msgstr "به طور مثال، مقدار و ارزش موجودی"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "e.g. John Smith"
msgstr "به طورمثال جان اسمیت"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "e.g. Tennis Court 1"
msgstr "به طورمثال زمین تنیس 1"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"e.g. <EMAIL>\r\n"
"e.g. <EMAIL>\r\n"
"..."
msgstr ""
"به طورمثال  <EMAIL>\n"
"به طورمثال  <EMAIL>\n"
"..."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid ""
"e.g. <EMAIL> \r\n"
"e.g. <EMAIL>\r\n"
"..."
msgstr ""
"به طور مثال  <EMAIL>\n"
"به طور مثال <EMAIL>\n"
"..."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "e.g. <EMAIL>"
msgstr "به طور مثال <EMAIL>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "from"
msgstr "از"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "has no availability for an appointment."
msgstr "به هیچ وجه امکان تنظیم قرار ملاقات وجود ندارد."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "has no more slots available for this month."
msgstr "بازه‌ی زمانی دیگیر برای این ماه وجود ندارد."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "hours before"
msgstr "چند ساعت قبل از"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "hours from now!"
msgstr "ساعت از الان!"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_details
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "people"
msgstr "افراد"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "persons)"
msgstr "اشخاص)"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "somebody"
msgstr "فردی"

#. module: appointment
#: model:onboarding.onboarding.step,description:appointment.appointment_onboarding_create_appointment_type_step
msgid "to automate appointments"
msgstr "تنظیم خودکار قرار ملاقات‌ها"

#. module: appointment
#: model:onboarding.onboarding.step,description:appointment.appointment_onboarding_preview_invite_step
msgid "to schedule appointments"
msgstr "برنامه‌ریزی یک قرار ملاقات"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "total capacity"
msgstr "ظرفیت کل"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "when over"
msgstr "هنگامی که تمام شود"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "with"
msgstr "با"
