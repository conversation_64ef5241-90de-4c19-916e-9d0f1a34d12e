# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* appointment
# 
# Translators:
# <PERSON><PERSON><PERSON> <j<PERSON>@image.dk>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-26 16:10+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid " (copy)"
msgstr " (kopi)"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_booking_line.py:0
#, python-format
msgid "\"%(resource_name_list)s\" cannot be used for \"%(appointment_type_name)s\""
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_count
msgid "# Appointments"
msgstr "# Aftaler"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_count_report
msgid "# Appointments in the last 30 days"
msgstr "# Aftaler i de seneste 30 dage"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__calendar_event_count
msgid "# Bookings"
msgstr "# Bookinger"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_invite_count
msgid "# Invitation Links"
msgstr "# Invitationslinks"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_resource_count
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_count
msgid "# Resources"
msgstr "# Ressourcer"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_staff_user_count
#: model:ir.model.fields,field_description:appointment.field_appointment_type__staff_user_count
msgid "# Staff Users"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "#{day['today_cls'] and 'Today' or ''}"
msgstr "#{day['today_cls'] and 'I dag' or ''}"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "%(appointment_name)s with %(partner_name)s"
msgstr "%(appointment_name)s med %(partner_name)s"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "%(attendee_name)s - %(appointment_name)s Booking"
msgstr "%(attendee_name)s - %(appointment_name)s Booking"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_resource.py:0
#, python-format
msgid "%(original_name)s (copy)"
msgstr "%(original_name)s (kopi)"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "%s - Let's meet"
msgstr "%s - Lad os mødes"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "(Total:"
msgstr "(Samlet:"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/calendar.py:0
#, python-format
msgid ", All Day"
msgstr ", Hele Dagen"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_svg.xml:0
#, python-format
msgid ""
".stgrey0{fill:#E3E3E3}\n"
"                .stgrey1{fill:#F2F2F2}"
msgstr ""
".stgrey0{fill:#E3E3E3}\n"
"                .stgrey1{fill:#F2F2F2}"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid ""
"<br/>\n"
"                                        <span>Duration</span>"
msgstr ""
"<br/>\n"
"                                        <span>Varighed</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid ""
"<br/>\n"
"                                    <span>(Last 30 Days)</span>"
msgstr ""
"<br/>\n"
"                                    <span>(Seneste 30 dage)</span>"

#. module: appointment
#: model:mail.template,body_html:appointment.attendee_invitation_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br><br>\n"
"\n"
"        <t t-if=\"target_customer\">\n"
"            Your appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> <t t-if=\"object.event_id.appointment_type_id.category != 'custom' and object.event_id.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.event_id.user_id.name or ''\">Ready Mat</t></t> has been booked.\n"
"            <t t-if=\"object.state != 'accepted' and object.event_id.appointment_type_id.schedule_based_on == 'resources' and object.event_id.appointment_type_id.resource_manual_confirmation\">\n"
"                You will receive a mail of confirmation with more details when your appointment will be confirmed.\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                <t t-out=\"customer.name or ''\"></t> scheduled the following appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> with you.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Your appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> has been booked.\n"
"            </t>\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"object.state != 'accepted'\">\n"
"            <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Accept</a>\n"
"            <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Decline</a>\n"
"        </t>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\"><t t-out=\"'Reschedule' if target_customer else 'View'\">View</t></a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <li>Appointment Type: <t t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</t></li>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <li t-if=\"object.event_id.appointment_type_id.resource_manage_capacity\">\n"
"                    For: <t t-out=\"object.event_id.resource_total_capacity_reserved\"></t> people\n"
"                </li>\n"
"                <li t-if=\"object.event_id.appointment_type_id.assign_method != 'time_auto_assign'\">\n"
"                    Resources\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.event_id.appointment_resource_ids\" t-as=\"resource\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"resource.name or ''\">Table 1</span>\n"
"                        </li>\n"
"                    </ul>\n"
"                </li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hej <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br><br>\n"
"\n"
"        <t t-if=\"target_customer\">\n"
"            Din aftale <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> <t t-if=\"object.event_id.appointment_type_id.category != 'custom' and object.event_id.appointment_type_id.schedule_based_on == 'users'\"> med <t t-out=\"object.event_id.user_id.name or ''\">Ready Mat</t></t> er blevet booket.\n"
"            <t t-if=\"object.state != 'accepted' and object.event_id.appointment_type_id.schedule_based_on == 'resources' and object.event_id.appointment_type_id.resource_manual_confirmation\">\n"
"                Du vil modtage en email med yderligere oplysninger så snart din aftale er blevet bekræftet.\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                <t t-out=\"customer.name or ''\"></t> har booket følgende aftale <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> med dig.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Din aftale <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> er blevet booket.\n"
"            </t>\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"object.state != 'accepted'\">\n"
"            <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Accepter</a>\n"
"            <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Afvis</a>\n"
"        </t>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\"><t t-out=\"'Reschedule' if target_customer else 'View'\">Gennemse</t></a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Aftale detaljer</strong></p>\n"
"            <ul>\n"
"                <li>Aftale type: <t t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</t></li>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Sted: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">Se på kort</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>Hvornår: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Varighed: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Deltagere\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">Dig</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <li t-if=\"object.event_id.appointment_type_id.resource_manage_capacity\">\n"
"                    For: <t t-out=\"object.event_id.resource_total_capacity_reserved\"></t> personer\n"
"                </li>\n"
"                <li t-if=\"object.event_id.appointment_type_id.assign_method != 'time_auto_assign'\">\n"
"                    Ressource\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.event_id.appointment_resource_ids\" t-as=\"resource\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"resource.name or ''\">Tabel 1</span>\n"
"                        </li>\n"
"                    </ul>\n"
"                </li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        Sådan deltager du:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Deltag med Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Deltag med</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Aftale beskrivelse:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Mange tak\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_booked_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <p>\n"
"    Appointment booked for <t t-out=\"object.appointment_type_id.name or ''\">Technical Demo</t>\n"
"    <t t-if=\"object.appointment_type_id.category != 'custom' and object.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t>.\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/join?token={{ object.access_token }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Join</a>\n"
"        <a t-attf-href=\"/web?#id={{ object.id }}&amp;view_type=form&amp;model=calendar.event\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;d&quot;, lang_code=object.env.lang) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div>\n"
"                            <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t>\n"
"                        </div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"></t>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"></td>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <p><strong>Details of the event</strong></p>\n"
"                <ul>\n"
"                    <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                    </li>\n"
"                    <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                    <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                    <li>Attendees\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                            <t t-if=\"attendee.common_name\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <span style=\"margin-left:5px\">You</span>\n"
"                            </t>\n"
"                        </li>\n"
"                    </ul></li>\n"
"                    <li t-if=\"object.appointment_type_id.resource_manage_capacity\">\n"
"                        For: <t t-out=\"object.resource_total_capacity_reserved\"></t> people\n"
"                    </li>\n"
"                    <li t-if=\"object.appointment_type_id.assign_method != 'time_auto_assign'\">\n"
"                        Resources\n"
"                        <ul>\n"
"                            <li t-foreach=\"object.appointment_resource_ids\" t-as=\"resource\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"resource.name or ''\">Table 1</span>\n"
"                            </li>\n"
"                        </ul>\n"
"                    </li>\n"
"                    <li t-if=\"object.videocall_redirection\">\n"
"                        How to Join:\n"
"                        <t t-if=\"object.videocall_source == 'discuss'\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-attf-href=\"{{ object.videocall_redirection }}\" target=\"_blank\" t-out=\"object.videocall_redirection or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </ul>\n"
"                <t t-if=\"not is_html_empty(object.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.description\"></t></li>\n"
"                </t>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <p>\n"
"    Aftale er booket til <t t-out=\"object.appointment_type_id.name or ''\">Technical Demo</t>\n"
"    <t t-if=\"object.appointment_type_id.category != 'custom' and object.appointment_type_id.schedule_based_on == 'users'\"> medh <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t>.\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/join?token={{ object.access_token }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Deltag</a>\n"
"        <a t-attf-href=\"/web?#id={{ object.id }}&amp;view_type=form&amp;model=calendar.event\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Gennemse</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;d&quot;, lang_code=object.env.lang) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div>\n"
"                            <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t>\n"
"                        </div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"></t>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"></td>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <p><strong>Aftale detaljer</strong></p>\n"
"                <ul>\n"
"                    <li t-if=\"object.location\">Sted: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">Se på kort</a>)\n"
"                    </li>\n"
"                    <li t-if=\"recurrent\">Hvornår: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                    <li t-if=\"not object.allday and object.duration\">Varighed: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                    <li>Deltagere\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                            <t t-if=\"attendee.common_name\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <span style=\"margin-left:5px\">Dig</span>\n"
"                            </t>\n"
"                        </li>\n"
"                    </ul></li>\n"
"                    <li t-if=\"object.appointment_type_id.resource_manage_capacity\">\n"
"                        For: <t t-out=\"object.resource_total_capacity_reserved\"></t> people\n"
"                    </li>\n"
"                    <li t-if=\"object.appointment_type_id.assign_method != 'time_auto_assign'\">\n"
"                        Resources\n"
"                        <ul>\n"
"                            <li t-foreach=\"object.appointment_resource_ids\" t-as=\"resource\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"resource.name or ''\">Tabel 1</span>\n"
"                            </li>\n"
"                        </ul>\n"
"                    </li>\n"
"                    <li t-if=\"object.videocall_redirection\">\n"
"                        Sådan deltager du:\n"
"                        <t t-if=\"object.videocall_source == 'discuss'\"> Deltag med Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Deltag her</t><br>\n"
"                        <a t-attf-href=\"{{ object.videocall_redirection }}\" target=\"_blank\" t-out=\"object.videocall_redirection or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </ul>\n"
"                <t t-if=\"not is_html_empty(object.description)\">\n"
"                    <li>Aftale beskrivelse:\n"
"                    <t t-out=\"object.description\"></t></li>\n"
"                </t>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_canceled_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <p>\n"
"    The appointment for <t t-out=\"object.appointment_type_id.name or ''\">Technical Demo</t> <t t-if=\"object.appointment_type_id.category != 'custom' and object.appointment_type_id.schedule_based_on == 'users'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t> has been canceled.\n"
"    </p>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div><t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t></div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"></t>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"></td>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <del>\n"
"                    <p><strong>Details of the event</strong></p>\n"
"                    <ul>\n"
"                            <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                            </li>\n"
"                            <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                            <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                        <li>Attendees\n"
"                        <ul t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <li>\n"
"                                <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                                <t t-if=\"attendee.common_name\">\n"
"                                    <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\"></span>\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <span style=\"margin-left:5px\">You</span>\n"
"                                </t>\n"
"                            </li>\n"
"                        </ul></li>\n"
"                        <li t-if=\"object.videocall_location\">\n"
"                            How to Join:\n"
"                            <t t-if=\"object.get_base_url() in object.videocall_location\"> Join with Odoo Discuss</t>\n"
"                            <t t-else=\"\"> Join at</t><br>\n"
"                            <a t-attf-href=\"{{ object.videocall_location }}\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                        </li>\n"
"                    </ul>\n"
"                    <t t-if=\"not is_html_empty(object.description)\">\n"
"                        <li>Description of the event:\n"
"                        <t t-out=\"object.description\"></t></li>\n"
"                    </t>\n"
"                </del>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <p>\n"
"    Aftalen til <t t-out=\"object.appointment_type_id.name or ''\">Technical Demo</t> <t t-if=\"object.appointment_type_id.category != 'custom' and object.appointment_type_id.schedule_based_on == 'users'\"> med <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t> er blevet aflyst.\n"
"    </p>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div><t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t></div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"></t>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"></td>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <del>\n"
"                    <p><strong>Aftale detaljer</strong></p>\n"
"                    <ul>\n"
"                            <li t-if=\"object.location\">Sted: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">Se på kort</a>)\n"
"                            </li>\n"
"                            <li t-if=\"recurrent\">Hvornår: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                            <li t-if=\"not object.allday and object.duration\">Varighed: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                        <li>Deltagere\n"
"                        <ul t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <li>\n"
"                                <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                                <t t-if=\"attendee.common_name\">\n"
"                                    <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\"></span>\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <span style=\"margin-left:5px\">Dig</span>\n"
"                                </t>\n"
"                            </li>\n"
"                        </ul></li>\n"
"                        <li t-if=\"object.videocall_location\">\n"
"                            Sådan deltager du:\n"
"                            <t t-if=\"object.get_base_url() in object.videocall_location\"> Deltag med Odoo Discuss</t>\n"
"                            <t t-else=\"\"> Deltag her</t><br>\n"
"                            <a t-attf-href=\"{{ object.videocall_location }}\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                        </li>\n"
"                    </ul>\n"
"                    <t t-if=\"not is_html_empty(object.description)\">\n"
"                        <li>Aftale beskrivelse:\n"
"                        <t t-out=\"object.description\"></t></li>\n"
"                    </t>\n"
"                </del>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "<i class=\"fa fa-info-circle\" title=\"Info\"/>"
msgstr "<i class=\"fa fa-info-circle\" title=\"Info\"/>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid ""
"<i class=\"fa fa-pencil me-2\" role=\"img\" aria-label=\"Edit\" "
"title=\"Create custom questions in backend\"/>Add Custom Questions"
msgstr ""
"<i class=\"fa fa-pencil me-2\" role=\"img\" aria-label=\"Edit\" "
"title=\"Create custom questions in backend\"/>Tilføj brugerspørgsmål"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "<i class=\"fa fa-plus me-1\"/> Add Guests"
msgstr "<i class=\"fa fa-plus me-1\"/> Tilføj gæster"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-plus me-1\"/>Add Guests"
msgstr "<i class=\"fa fa-plus me-1\"/>Tilføj gæster"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_details
msgid ""
"<i class=\"fa fa-video-camera fa-fw me-2 mt-1 text-muted\"/>\n"
"                <span class=\"o_not_editable\">Online</span>"
msgstr ""
"<i class=\"fa fa-video-camera fa-fw me-2 mt-1 text-muted\"/>\n"
"                <span class=\"o_not_editable\">Online</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid ""
"<i class=\"fa fa-warning me-2\"/>\n"
"                    <span invisible=\"schedule_based_on != 'users'\">Impossible to share a link for an appointment type that has no user assigned.</span>\n"
"                    <span invisible=\"schedule_based_on != 'resources'\">Impossible to share a link for an appointment type that has no resource assigned.</span>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid ""
"<i class=\"fa fa-warning me-2\"/>\n"
"                    <span invisible=\"schedule_based_on != 'users'\">You need to be part of an appointment type to be able to share a personal link.</span>\n"
"                    <span invisible=\"schedule_based_on != 'resources'\">You can't create a personal link for an appointment type based on resources.</span>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_date
msgid "<small class=\"text-uppercase text-muted\">Date &amp; time</small>"
msgstr "<small class=\"text-uppercase text-muted\">Dato &amp; tid</small>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_details
msgid "<small class=\"text-uppercase text-muted\">Meeting details</small>"
msgstr "<small class=\"text-uppercase text-muted\">Møde detaljer</small>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"d-block\">Scheduled</span>"
msgstr "<span class=\"d-block\">Booket</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-globe\"/> Preview"
msgstr "<span class=\"fa fa-globe\"/> Forhåndsvisning"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-pencil\"/> Edit"
msgstr "<span class=\"fa fa-pencil\"/> Redigér"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-share-alt\"/> Share"
msgstr "<span class=\"fa fa-share-alt\"/> Del"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"fa fa-trash\"/> Delete"
msgstr "<span class=\"fa fa-trash\"/> Slet"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid ""
"<span class=\"px-2\" invisible=\"start_datetime or category == "
"'anytime'\">or</span>"
msgstr ""
"<span class=\"px-2\" invisible=\"start_datetime or category == "
"'anytime'\">eller</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">Arkiveret</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "<span> hours</span>"
msgstr "<span> timer</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<span>Online</span>"
msgstr "<span>Online</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid ""
"<span>You are scheduling a booking outside the available hours of</span>"
msgstr ""
"<span>Du er ved at booke en aftale udenfor de tilgængelige timer for</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "<span>people</span>"
msgstr "<span>mennesker</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment canceled!</strong>\n"
"                                            You can schedule another appointment from here."
msgstr ""
"<strong>Aftale er annulleret!</strong>\n"
"                                            Du kan booke en anden aftaler herfra."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                                            The selected timeslot is not available anymore.\n"
"                                            Someone has booked the same time slot a few\n"
"                                            seconds before you."
msgstr ""
"<strong>Booking mislykkedes!</strong>\n"
"                                            Det valgte tidsinterval er ikke længere ledigt.\n"
"                                            En anden har booket den samme tid nogle\n"
"                                            sekunder før dig."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                                            The selected timeslot is not available.\n"
"                                            It appears you already have another meeting with us at that date."
msgstr ""
"<strong>Booking mislykkedes!</strong>\n"
"                                            Det valgte tidsinterval er ikke ledigt.\n"
"                                            Det ser ud til, at du allerede har en aftale med os på den dato."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Booked for: </strong>"
msgstr "<strong>Booket til: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Contact Information</strong>"
msgstr "<strong>Kontaktinformation</strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Email: </strong>"
msgstr "<strong>E-mail: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Name: </strong>"
msgstr "<strong>Navn: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Phone: </strong>"
msgstr "<strong>Telefon: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Start Date: </strong>"
msgstr "<strong>Start dato: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>Stop Dato: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "<strong>Type: </strong>"
msgstr "<strong>Type: </strong>"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "A %s appointment type shouldn't be limited by datetimes."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/calendar_view.py:0
#, python-format
msgid ""
"A list of slots information is needed to create a custom appointment type"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid ""
"A punctual appointment type should be limited between a start and end "
"datetime."
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__access_token
msgid "Access Token"
msgstr "Adgangstoken"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/kanban/kanban_record.xml:0
#, python-format
msgid "Action"
msgstr "Handling"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_needaction
msgid "Action Needed"
msgstr "Handling påkrævet"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__active
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__active
#: model:ir.model.fields,field_description:appointment.field_appointment_type__active
msgid "Active"
msgstr "Aktiv"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/gantt/gantt_renderer.js:0
#: code:addons/appointment/static/src/views/gantt/gantt_renderer.xml:0
#: code:addons/appointment/static/src/views/list/list_renderer.js:0
#: code:addons/appointment/static/src/views/list/list_renderer.xml:0
#, python-format
msgid "Add Closing Day(s)"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Add Guests"
msgstr "Tilføj gæster"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated_card
msgid "Add a function here..."
msgstr "Tilføj en funktion her..."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated_card
msgid "Add a resource description here..."
msgstr "Tilføj en ressourcebeskrivelse her..."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Add a specific appointment"
msgstr "Tilføj en specifik aftale."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Add an intro message here..."
msgstr "Tilføj en introbesked her..."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Add more details about you"
msgstr "Tilføj flere detaljer om dig"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_manage_leaves
msgid "Add or remove leaves from appointments"
msgstr "Tilføj eller fjern ferie/fri fra aftaler"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Add to Google Agenda"
msgstr "Tilføj til Google Agenda"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Add to iCal/Outlook"
msgstr "Tilføj til iCal/Outlook"

#. module: appointment
#: model:res.groups,name:appointment.group_appointment_manager
msgid "Administrator"
msgstr "Administrator"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Alle"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_report_all
#: model:ir.ui.menu,name:appointment.menu_schedule_report_all_events
msgid "All Appointments"
msgstr "Alle aftaler"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__allday
#, python-format
msgid "All day"
msgstr "Hele dagen"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Allow Cancelling"
msgstr "Tillad annullering"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__allow_guests
msgid "Allow Guests"
msgstr "Tillad gæster"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__country_ids
msgid "Allowed Countries"
msgstr "Tilladte lande"

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_answer_input_value_check
msgid "An answer input must either have a text value or a predefined answer."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/calendar_view.py:0
#, python-format
msgid "An appointment type is needed to get the link."
msgstr "En aftaletype er nødvendig for at få linket."

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_calendar_event_check_resource_and_appointment_type
msgid "An event cannot book resources without an appointment type."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_slot.py:0
#, python-format
msgid "An unique type slot should have a start and end datetime"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__name
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_view_form
msgid "Answer"
msgstr "Svar"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_answer_input_action_from_question
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_graph
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_pivot
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_tree
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Answer Breakdown"
msgstr "Svar Opdeling"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_form
msgid "Answer Input"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Answers"
msgstr "Svar"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__anytime
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
#, python-format
msgid "Any Time"
msgstr "Når som helst"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_invite__resources_choice__all_assigned_resources
msgid "Any User/Resource"
msgstr "Enhver bruger/ressource"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Anytime appointment types should only have one user but got %s users"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_id
#, python-format
msgid "Appointment"
msgstr "Aftale"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_answer_input
msgid "Appointment Answer Inputs"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_answer_input_ids
msgid "Appointment Answers"
msgstr "Aftale svar"

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_booked
#: model:mail.message.subtype,name:appointment.mt_appointment_type_booked
#: model:mail.message.subtype,name:appointment.mt_calendar_event_booked
msgid "Appointment Booked"
msgstr "Aftale bekræftet"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_booked_mail_template
msgid "Appointment Booked: {{ object.appointment_type_id.name }}"
msgstr "Aftale bekræftet: {{ object.appointment_type_id.name }}"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_booking_line
msgid "Appointment Booking Line"
msgstr ""

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_canceled
#: model:mail.message.subtype,name:appointment.mt_appointment_type_canceled
#: model:mail.message.subtype,name:appointment.mt_calendar_event_canceled
msgid "Appointment Canceled"
msgstr "Aftale annulleret"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_canceled_mail_template
msgid "Appointment Canceled: {{ object.appointment_type_id.name }}"
msgstr "Aftale annulleret: {{ object.appointment_type_id.name }}"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form
msgid "Appointment Details"
msgstr "Aftale detaljer"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_duration_formatted
msgid "Appointment Duration Formatted "
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__appointment_duration_formatted
msgid "Appointment Duration formatted in words"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Appointment Duration should be higher than 0.00."
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_invite_id
msgid "Appointment Invitation"
msgstr "Aftaleinvitation"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_search
msgid "Appointment Invitation Links"
msgstr "Aftaleinvitationslinks"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree_invitation
msgid "Appointment Invitations"
msgstr "Aftaleinvitationer"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_invite
msgid "Appointment Invite"
msgstr "Aftaleinvitation"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__meeting_ids
msgid "Appointment Meetings"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointment Name"
msgstr "Aftalenavn"

#. module: appointment
#: model:onboarding.onboarding,name:appointment.onboarding_onboarding_appointment
msgid "Appointment Onboarding"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_answer
msgid "Appointment Question Answers"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_question
msgid "Appointment Questions"
msgstr "Aftale spørgsmål"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_resource
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__appointment_resource_id
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__name
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_resource_id
msgid "Appointment Resource"
msgstr "Aftale ressource"

#. module: appointment
#: model:ir.actions.server,name:appointment.resource_calendar_leaves_action_show_appointment_resources
msgid "Appointment Resource Leaves"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_resource_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
msgid "Appointment Resources"
msgstr "Aftale ressourcer"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_resource_action
msgid ""
"Appointment Resources are the places or equipment people can book\n"
"                (e.g. Tables, Tennis Courts, Meeting Rooms, ...)"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__name
msgid "Appointment Title"
msgstr "Aftale titel"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_type
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_appointment_question__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__appointment_type_id
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_search
msgid "Appointment Type"
msgstr "Aftale type"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__appointment_type_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Appointment Types"
msgstr "Aftale typer"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Appointment canceled"
msgstr "Aftale annulleret"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Appointment canceled by: %(partners)s"
msgstr "Aftale annulleret af: %(partners)s"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Appointment re-booked"
msgstr "Aftale gen-booket"

#. module: appointment
#: model:mail.template,name:appointment.appointment_booked_mail_template
msgid "Appointment: Appointment Booked"
msgstr "Aftale: Aftale booket"

#. module: appointment
#: model:mail.template,name:appointment.appointment_canceled_mail_template
msgid "Appointment: Appointment Canceled"
msgstr "Aftale: Aftale annulleret"

#. module: appointment
#: model:mail.template,name:appointment.attendee_invitation_mail_template
msgid "Appointment: Attendee Invitation"
msgstr "Aftale: Deltagerinvitation"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_slot
msgid "Appointment: Time Slot"
msgstr "Aftale: Tidsrum"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_type_action
#: model:ir.actions.act_window,name:appointment.calendar_event_action_appointment_reporting
#: model:ir.ui.menu,name:appointment.appointment_menu_calendar
#: model:ir.ui.menu,name:appointment.appointment_type_menu
#: model:ir.ui.menu,name:appointment.main_menu_appointments
#: model:ir.ui.menu,name:appointment.menu_schedule_report_all
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_graph
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_pivot
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_search
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_menu_appointment
msgid "Appointments"
msgstr "Aftaler"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointments by"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Archived"
msgstr "Arkiveret"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__resources_choice
msgid "Assign to"
msgstr "Tildel til"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__assign_method
msgid "Assignment Method"
msgstr "Tildelings Metode"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_slot.py:0
#, python-format
msgid ""
"At least one slot duration is shorter than the meeting duration (%s hours)"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_attachment_count
msgid "Attachment Count"
msgstr "Antal vedhæftninger"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Attendees"
msgstr "Deltagere"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_attended
msgid "Attendees Arrived"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__slot_ids
msgid "Availabilities"
msgstr "Tilgængeligheder"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__schedule_based_on
#: model:ir.model.fields,field_description:appointment.field_appointment_type__schedule_based_on
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_schedule_based_on
msgid "Availability on"
msgstr "Til rådighed den"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__answer_ids
msgid "Available Answers"
msgstr "Mulige svar"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
msgid "Available In"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__appointment_type_ids
msgid "Available in"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_1920
msgid "Avatar"
msgstr "Avatar"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_1024
msgid "Avatar 1024"
msgstr "Profilikon 1024"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_128
msgid "Avatar 128"
msgstr "Profilikon 128"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_256
msgid "Avatar 256"
msgstr "Profilikon 256"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__avatar_512
msgid "Avatar 512"
msgstr "Profilikon 512"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Back to edit mode"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__base_book_url
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__base_book_url
msgid "Base Link URL"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__calendar_event_ids
msgid "Booked Appointments"
msgstr "Bookede aftaler"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Booked by"
msgstr "Booket af"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__calendar_event_id
msgid "Booking"
msgstr "Booking"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "Booking Details"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__event_stop
msgid "Booking End"
msgstr "Booking afslutning"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__booking_line_ids
msgid "Booking Lines"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Booking Name"
msgstr "Bookingnavn"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__event_start
msgid "Booking Start"
msgstr "Booking start"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Bookings"
msgstr "Bookinger"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_partner_ids
msgid "CC to"
msgstr "CC til"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Oplysninger på kalenderdeltagere "

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_event
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__calendar_event_id
msgid "Calendar Event"
msgstr "Kalender begivenhed"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_onboarding_link_view_form
msgid "Cancel"
msgstr "Annullér"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__min_cancellation_hours
msgid "Cancel Before (hours)"
msgstr "Aflys før (timer)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Cancel/Reschedule"
msgstr "Annullér/Planlæg ny tid"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__canceled_mail_template_id
msgid "Cancelation Email"
msgstr "Annulleringsemail"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__capacity
msgid "Capacity"
msgstr "Kapacitet"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_manual_confirmation_percentage
msgid "Capacity Percentage"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__capacity_reserved
msgid "Capacity Reserved"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__capacity_used
msgid "Capacity Used"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__capacity_reserved
msgid "Capacity reserved by the user"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__capacity_used
msgid "Capacity that will be used based on the capacity and resource selected"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__category
msgid "Category"
msgstr "Kategori"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__checkbox
msgid "Checkboxes (multiple answers)"
msgstr "Afkrydsnings bokse (flere svar)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Choose your appointment"
msgstr "Vælg din aftale"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__company_id
msgid "Company"
msgstr "Virksomhed"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__disable_save_button
msgid "Computes if alert is present"
msgstr ""

#. module: appointment
#: model:ir.ui.menu,name:appointment.appointment_menu_config
msgid "Configuration"
msgstr "Konfiguration"

#. module: appointment
#: model:onboarding.onboarding.step,button_text:appointment.appointment_onboarding_create_appointment_type_step
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Configure"
msgstr "Konfigurer"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_invite_action
msgid ""
"Configure links that allow booking appointments with custom settings<br>\n"
"                (e.g. a specific user only, a list of appointment types, ...)"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_resources.xml:0
#, python-format
msgid "Confirm"
msgstr "Bekræft"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Confirm Appointment"
msgstr "Bekræft aftale"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/gantt/gantt_popover.xml:0
#, python-format
msgid "Confirm Check-In"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__booked_mail_template_id
msgid "Confirmation Email"
msgstr "Bekræftelseemail"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_confirmation
msgid "Confirmation Message"
msgstr "Bekræftelses besked"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Confirmed"
msgstr "Bekræftet"

#. module: appointment
#: model:onboarding.onboarding.step,button_text:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "Connect"
msgstr "Forbind"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Connect your Calendar"
msgstr "Forbind din kalender"

#. module: appointment
#: model:onboarding.onboarding.step,title:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "Connect your calendar"
msgstr "Forbind din kalender"

#. module: appointment
#: model:ir.model,name:appointment.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Contact Details:"
msgstr "Kontaktoplysninger"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_partner_ids
msgid ""
"Contacts that need to be notified whenever a new appointment is booked or "
"canceled,                                                  regardless of "
"whether they attend or not"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Continue <span class=\"oi oi-arrow-right\"/>"
msgstr "Fortsæt <span class=\"oi oi-arrow-right\"/>"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.js:0
#, python-format
msgid "Copied!"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/components/appointment_invite/appointment_invite_copy_close.xml:0
#: code:addons/appointment/static/src/components/appointment_onboarding/appointment_onboarding_invite_buttons.xml:0
#, python-format
msgid "Copy Link & Close"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Create Closing Day(s)"
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_resource_action
msgid "Create an Appointment Resource"
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action_custom
msgid ""
"Create invites on the fly from your calendar and share them with anyone by "
"using the Share Availabilities button."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Create your first Appointment"
msgstr "Opret din første aftale"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_question__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__create_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_type__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_question__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__create_date
#: model:ir.model.fields,field_description:appointment.field_appointment_type__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__custom
msgid "Custom"
msgstr "Tilpasset"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Custom Link"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__partner_id
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Customer"
msgstr "Kunde"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL APPOINTMENTS"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#, python-format
msgid "Date"
msgstr "Dato"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Date &amp; time"
msgstr "Dato &amp; tid"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Dates"
msgstr "Datoer"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Declined"
msgstr "Afslået"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Default slots cannot be applied to the %s appointment type category."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__slot_type
msgid ""
"Defines the type of slot. The recurring slot is the default type which is used for\n"
"        appointment type that are used recurringly in type like medical appointment.\n"
"        The one shot type is only used when an user create a custom appointment type for a client by\n"
"        defining non-recurring time slot (e.g. 10th of April 2021 from 10 to 11 am) from its calendar."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__event_videocall_source
msgid ""
"Defines the type of video call link that will be used for the generated "
"events. Keep it empty to prevent generating meeting url."
msgstr ""

#. module: appointment
#: model:appointment.type,name:appointment.appointment_type_dental_care
msgid "Dental Care"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__description
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "Description"
msgstr "Beskrivelse"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__destination_resource_ids
msgid "Destination combination"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Details"
msgstr "Detaljer:"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__allday
msgid ""
"Determine if the slot englobe the whole day, mainly used for unique slot "
"type"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form_insert_link
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
#, python-format
msgid "Discard"
msgstr "Kassér"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_question__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__display_name
#: model:ir.model.fields,field_description:appointment.field_appointment_type__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__avatars_display
msgid "Display the Users'/Resources' picture on the Website."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__resource_manual_confirmation
msgid ""
"Do not automatically accept meetings created from the appointment once the total capacity\n"
"            reserved for a slot exceeds the percentage chosen. The appointment is still considered as reserved for\n"
"            the slots availability."
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__select
msgid "Dropdown (one answer)"
msgstr "Dropdown (ét svar)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__duration
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_duration
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Duration"
msgstr "Varighed"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Email*"
msgstr "Email*"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Email: %(email)s"
msgstr "Email: %(email)s"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/appointment.py:0
#, python-format
msgid "Email: %s"
msgstr "Email: %s"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__leave_end_dt
msgid "End Date"
msgstr "Slut dato"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__end_datetime
msgid "End Datetime"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__end_datetime
msgid "End datetime for unique slot type management"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__end_hour
msgid "Ending Hour"
msgstr "Sluttid"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_alarm
msgid "Event Alarm"
msgstr "Arrangements notifikation"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "Event Details"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Every"
msgstr "Hver"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Extra Comments..."
msgstr "Ekstra kommentarer..."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Extra Message on Confirmation"
msgstr "Ekstra besked på bekræftelse"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_confirmation
msgid "Extra information provided once the appointment is booked."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_appointment
msgid "Follow, reschedule or cancel your appointments"
msgstr "Følg, ændre eller aflys dine bookinger"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "For"
msgstr "Til"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__5
msgid "Friday"
msgstr "Fredag"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "From"
msgstr "Fra"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__avatars_display
msgid "Front-End Display"
msgstr "Front-end visning"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Full name*"
msgstr "Fulde navn*"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Get Share Link"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#: model_terms:ir.ui.view,arch_db:appointment.appointment_onboarding_link_view_form
#, python-format
msgid "Get Your Link"
msgstr "Hent dit link"

#. module: appointment
#: model:ir.model,name:appointment.model_appointment_onboarding_link
msgid "Get a link to an appointment type during the onboarding"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_invite__suggested_staff_user_ids
msgid ""
"Get the users linked to the appointment type selected to apply a domain on "
"the users that can be selected"
msgstr ""

#. module: appointment
#: model:onboarding.onboarding.step,title:appointment.appointment_onboarding_preview_invite_step
msgid "Get your link"
msgstr "Hent dit link"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Google Agenda"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_search
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Group By"
msgstr "Sortér efter"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Guest usage is limited to 10 customers for performance reason."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Guests"
msgstr "Gæster"

#. module: appointment
#: model:ir.model,name:appointment.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__has_message
msgid "Has Message"
msgstr "Har besked"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "How to join"
msgstr "Sådan deltager du"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__assign_method
msgid ""
"How users and resources will be assigned to meetings customers book on your "
"website."
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__id
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__id
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__id
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__id
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__id
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__id
#: model:ir.model.fields,field_description:appointment.field_appointment_question__id
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__id
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__id
#: model:ir.model.fields,field_description:appointment.field_appointment_type__id
msgid "ID"
msgstr "ID"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis afkrydset, kræver nye beskeder din opmærksomhed "

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_has_error
#: model:ir.model.fields,help:appointment.field_appointment_type__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis afkrydset har nogle beskeder en leveringsfejl"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "If empty, Odoo will not send emails"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__restrict_to_resource_ids
msgid ""
"If empty, all resources are considered to be available.\n"
"If set, only the selected resources will be taken into account for this slot."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__restrict_to_user_ids
msgid ""
"If empty, all users are considered to be available.\n"
"If set, only the selected users will be taken into account for this slot."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "If empty, the meeting is considered as taking place online"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__canceled_mail_template_id
msgid ""
"If set an email will be sent to the customer when the appointment is "
"canceled."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__booked_mail_template_id
msgid ""
"If set an email will be sent to the customer when the appointment is "
"confirmed."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Hvis det aktive felt er sat til Falsk, vil du kunne gemme ressourcen uden at"
" fjerne den."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Hvis det aktive felt er sat til sand, vil det give dig mulighed for at "
"skjule begivenheds alarmen uden at fjerne den."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_1920
msgid "Image"
msgstr "Billede"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_1024
msgid "Image 1024"
msgstr "Billede 1024"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_128
msgid "Image 128"
msgstr "Billede 128"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_256
msgid "Image 256"
msgstr "Billede 256"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__image_512
msgid "Image 512"
msgstr "Billede 512"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Insert Appointment Link"
msgstr "Indsæt aftalelink"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form_insert_link
msgid "Insert link"
msgstr "Indsæt link"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_intro
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Introduction Message"
msgstr "Introduktion besked"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/appointment_form.js:0
#: code:addons/appointment/static/src/js/appointment_validation.js:0
#, python-format
msgid "Invalid Email"
msgstr "Ugyldig e-mail."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_invite_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_tree
msgid "Invitation Links"
msgstr "Invitationslinks"

#. module: appointment
#: model:mail.template,description:appointment.attendee_invitation_mail_template
msgid "Invitation email to new attendees of an appointment"
msgstr ""

#. module: appointment
#: model:mail.template,subject:appointment.attendee_invitation_mail_template
msgid "Invitation to {{ object.event_id.name }}"
msgstr "Invitation til {{ object.event_id.name }}"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_type_action_custom
#: model:ir.ui.menu,name:appointment.menu_appointment_type_custom
msgid "Invitations"
msgstr "Invitationer"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__is_published
msgid "Is Published"
msgstr "Er udgivet"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"It's too late to cancel online, please contact the attendees another way if "
"you really can't make it."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Join at"
msgstr "Deltag her"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Join with Odoo Discuss"
msgstr "Deltag med Odoo Discuss"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__country_ids
msgid ""
"Keep empty to allow visitors from any country, otherwise you only allow "
"visitors from selected countries"
msgstr ""
"Lad stå tom for at tillade besøgende fra alle lande. Ellers tillader du kun "
"besøgende fra udvalgte lande"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_question__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__write_uid
#: model:ir.model.fields,field_description:appointment.field_appointment_type__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_booking_line__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_question__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__write_date
#: model:ir.model.fields,field_description:appointment.field_appointment_type__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__allow_guests
msgid "Let attendees invite guests when registering a meeting."
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Link Copied in your clipboard!"
msgstr "Link kopieret til udklipsholder."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "Link Generator"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__book_url
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "Link URL"
msgstr "Link URL"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/components/appointment_invite/appointment_invite_copy_close.js:0
#: code:addons/appointment/static/src/components/appointment_onboarding/appointment_onboarding_invite_buttons.js:0
#, python-format
msgid "Link copied to clipboard!"
msgstr "Link kopieret til udklipsholder."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__linked_resource_ids
msgid "Linked Resource"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__linked_resource_ids
msgid "List of resources that can be combined to handle a bigger demand."
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__location_id
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Location"
msgstr "Adresse"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__location
msgid "Location formatted"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__location
msgid "Location formatted for one line uses"
msgstr ""

#. module: appointment
#: model:onboarding.onboarding.step,done_text:appointment.appointment_onboarding_create_appointment_type_step
msgid "Looks great!"
msgstr "Ser godt ud!"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_resources.xml:0
#, python-format
msgid "Make your choice"
msgstr "Foretag dit valg"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_manage_capacity
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_manage_capacity
msgid "Manage Capacities"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__resource_manage_capacity
#: model:ir.model.fields,help:appointment.field_calendar_event__appointment_type_manage_capacity
msgid ""
"Manage the maximum amount of people a resource can handle (e.g. Table for 6 "
"persons, ...)"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_manual_confirmation
msgid "Manual Confirmation"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Max in"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__capacity
msgid ""
"Maximum amount of people for this resource (e.g. Table for 6 persons, ...)"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_invite__resources_choice__current_user
msgid "Me (only with Users)"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#: code:addons/appointment/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Meet With Me"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__videocall_redirection
msgid "Meeting redirection URL"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "Meetings"
msgstr "Møder"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_has_error
msgid "Message Delivery error"
msgstr "Besked ved leveringsfejl"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Message from the Organizer"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Messages"
msgstr "Beskeder"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Min"
msgstr "Min"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__1
msgid "Monday"
msgstr "Mandag"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "More Options"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__text
msgid "Multi-line text"
msgstr "Tekst med flere linjer"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "My Appointments"
msgstr "Mine aftaler"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_search
msgid "My Links"
msgstr "Mine links"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr "Navn"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Navigation"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_alarm__default_for_new_appointment_type
msgid "New Appointments Default"
msgstr "Standard ved nye aftaler"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_answer_input_action_from_question
msgid "No Answers yet!"
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action
msgid "No Appointment Configured"
msgstr "Ingen aftale konfigureret"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_view_bookings_resources
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_view_bookings_users
msgid "No Appointment or Resource were found."
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action_custom
msgid "No Custom Availabilities Shared!"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__avatars_display__hide
msgid "No Picture"
msgstr "Intet billede"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_invite_action
msgid "No Shared Links yet!"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__appointment_type_info_msg
msgid "No User Assigned Message"
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_appointment_reporting
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report_all
msgid "No data yet!"
msgstr "Ingen data endnu!"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "Ingen"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_event__alarm_ids
msgid "Notifications sent to all attendees to remind of the meeting."
msgstr "Beskeder sendt til alle deltagere for at minde om mødet."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal handlinger"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fejl"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antal meddelelser der kræver handling"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal beskeder med leveringsfejl"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Number of people"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__event_videocall_source__discuss
msgid "Odoo Discuss"
msgstr "Odoo Discuss"

#. module: appointment
#: model:ir.model,name:appointment.model_onboarding_onboarding
msgid "Onboarding"
msgstr "Onboarding"

#. module: appointment
#: model:onboarding.onboarding.step,step_image_alt:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "Onboarding Connect your calendar"
msgstr ""

#. module: appointment
#: model:onboarding.onboarding.step,step_image_alt:appointment.appointment_onboarding_preview_invite_step
msgid "Onboarding Get your link"
msgstr ""

#. module: appointment
#: model:onboarding.onboarding.step,step_image_alt:appointment.appointment_onboarding_create_appointment_type_step
msgid "Onboarding Set Your Availabilities"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "Onboarding Trin"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__slot_type__unique
msgid "One Shot"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid ""
"Only letters, numbers, underscores and dashes are allowed in your links."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_invite.py:0
#, python-format
msgid ""
"Only letters, numbers, underscores and dashes are allowed in your links. You"
" need to adapt %s."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid "Only one anytime appointment type is allowed for a specific user."
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Open"
msgstr "Åben"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "Opening Hours"
msgstr "Åbningstider"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
msgid "Operator"
msgstr "Operatør"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Options"
msgstr "Valgmuligheder"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__user_id
msgid "Organizer"
msgstr "Arrangør"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Our first availability is"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Outlook"
msgstr "Outlook"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Past"
msgstr "Udløbet"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_booker_id
msgid "Person who is booking the appointment"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Phone number*"
msgstr "Telefonnummer*"

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "Phone: %(phone)s"
msgstr "Telefon: %(phone)s"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/appointment.py:0
#, python-format
msgid "Phone: %s"
msgstr "Telefon: %s"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__assign_method__resource_time
msgid "Pick User/Resource then Time"
msgstr "Vælg person/ressource og derefter tidspunkt"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Pick a Work Schedule..."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "Pick a schedule to restrict openings"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Pick your availabilities"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__placeholder
msgid "Placeholder"
msgstr "Pladsholder"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Please, select another date."
msgstr "Vælg venligst en anden dato."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_resource_ids
msgid "Possible resources"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__suggested_staff_user_ids
msgid "Possible users"
msgstr "Mulige brugere"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/components/appointment_onboarding/appointment_onboarding_invite_buttons.xml:0
#: model:onboarding.onboarding.step,button_text:appointment.appointment_onboarding_preview_invite_step
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#, python-format
msgid "Preview"
msgstr "Eksempel"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__punctual
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Punctual"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__question_id
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__question_id
#: model:ir.model.fields,field_description:appointment.field_appointment_question__name
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Question"
msgstr "Spørgsmål"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__question_type
#: model:ir.model.fields,field_description:appointment.field_appointment_question__question_type
msgid "Question Type"
msgstr "Spørgsmålstype"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__question_ids
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Questions"
msgstr "Spørgsmål"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__radio
msgid "Radio (one answer)"
msgstr "Radio (ét svar)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__rating_ids
msgid "Ratings"
msgstr "Bedømmelser"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__reason
msgid "Reason"
msgstr "Årsag"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__slot_type__recurring
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__category__recurring
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Recurring"
msgstr "Gentagende"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__redirect_url
msgid "Redirect URL"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__reminder_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_event__alarm_ids
#: model:ir.ui.menu,name:appointment.menu_appointment_reminders
msgid "Reminders"
msgstr "Påmindelser"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Remove"
msgstr "Fjern"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_schedule_report
#: model:ir.ui.menu,name:appointment.reporting_menu_calendar
msgid "Reporting"
msgstr "Rapportering"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__question_required
msgid "Required Answer"
msgstr "Påkrævet svar"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__resource_id
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Resource"
msgstr "Ressource"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_view_bookings_resources
#: model:ir.actions.server,name:appointment.calendar_event_action_all_resources_bookings
msgid "Resource Bookings"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__calendar_id
msgid "Resource Calendar"
msgstr "Ressource kalender"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_appointment_resource_leaves
msgid "Resource Leaves"
msgstr ""

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_resource_action
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__resource_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resource_ids
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__schedule_based_on__resources
#: model:ir.ui.menu,name:appointment.menu_appointment_resource
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree_invitation
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_gantt_booking_resource
msgid "Resources"
msgstr "Ressourcer"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_appointment_schedule_resource_booking
msgid "Resources Bookings"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resources_on_leave
msgid "Resources intersecting with leave time"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Responsible"
msgstr "Ansvarlig"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_tree
msgid "Restrict to Resource"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__restrict_to_resource_ids
msgid "Restrict to Resources"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_tree
msgid "Restrict to User"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__restrict_to_user_ids
msgid "Restrict to Users"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "Restrict to specific Resources"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "SCHEDULED"
msgstr "PLANLAGTE"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveringsfejl"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__6
msgid "Saturday"
msgstr "Lørdag"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Save"
msgstr "Gem"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_appointment_schedule_resources
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Schedule"
msgstr "Plan"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__schedule_based_on
msgid "Schedule Based On"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Schedule an Appointment"
msgstr "Planlæg en aftale"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_appointment_reporting
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report_all
msgid "Schedule appointments to get statistics"
msgstr "Planlæg aftaler for at indhente statistikker"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__min_schedule_hours
msgid "Schedule before (hours)"
msgstr "Planlæg før (timer)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__max_schedule_days
msgid "Schedule not after (days)"
msgstr "Planlæg ikke efter (dage)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Scheduling"
msgstr "Planlægning"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "Søg i alle"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr "Søg i Beskrivelse"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Name"
msgstr "Søg i Navn"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Responsible"
msgstr "Søg i Ansvarlig"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Select Dates"
msgstr "Vælg datoer"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__assign_method__time_resource
msgid "Select Time then User/Resource"
msgstr "Vælg tidspunkt og derefter person/ressource"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__assign_method__time_auto_assign
msgid "Select Time then auto-assign"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Select a date &amp; time"
msgstr "Vælg en dato &amp; tid"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_slots.xml:0
#, python-format
msgid "Select a time"
msgstr "Vælg et tidspunkt"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Select attendees..."
msgstr "Vælg deltagere..."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__value_answer_id
msgid "Selected Answer"
msgstr "Valgt svar"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__appointment_type_count
msgid "Selected Appointments Count"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Selection Questions"
msgstr ""

#. module: appointment
#: model:mail.template,description:appointment.appointment_canceled_mail_template
msgid "Sent to all attendees when an appointment is cancelled"
msgstr "Sendt til alle deltagere når en aftale er aflyst"

#. module: appointment
#: model:mail.template,description:appointment.appointment_booked_mail_template
msgid "Sent to followers of an appointment type when a meeting is booked"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer__sequence
#: model:ir.model.fields,field_description:appointment.field_appointment_question__sequence
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__sequence
#: model:ir.model.fields,field_description:appointment.field_appointment_type__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: appointment
#: model:onboarding.onboarding.step,title:appointment.appointment_onboarding_create_appointment_type_step
msgid "Set your availabilities"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_tree
msgid "Share"
msgstr "Del"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Share Appointment Link"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.xml:0
#, python-format
msgid "Share Availabilities"
msgstr ""

#. module: appointment
#. odoo-javascript
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#: code:addons/appointment/static/src/views/appointment_calendar/appointment_calendar_controller.js:0
#, python-format
msgid "Share Link"
msgstr "Del link"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.appointment_invite_action
#: model:ir.ui.menu,name:appointment.menu_appointment_invite
msgid "Share Links"
msgstr "Del links"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.appointment_type_action
msgid ""
"Share calendar link allowing people to book meetings with you, your team or "
"a resource."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_onboarding_link_view_form
msgid ""
"Share this link to let people book meetings with you, your team or a "
"resource."
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__shareable
msgid "Shareable"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Shared Links"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__short_code
#: model:ir.model.fields,field_description:appointment.field_appointment_onboarding_link__short_code
msgid "Short Code"
msgstr "Kort kode"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__short_code_format_warning
msgid "Short Code Format Warning"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__short_code_unique_warning
msgid "Short Code Unique Warning"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__avatars_display__show
msgid "Show Pictures"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_question__question_type__char
msgid "Single line text"
msgstr "Enkelt linje tekst"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__slot_type
msgid "Slot type"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__message_intro
msgid "Small description of the appointment type."
msgstr "Lille beskrivelse af aftaletypen."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry,"
msgstr "Beklager,"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, it is no longer possible to schedule an appointment."
msgstr "Desværre, det er ikke længere muligt af booke en aftale."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, there is not any more availability for the asked capacity."
msgstr "Desværre, der er ikke flere ledige tider for det ønskede antal."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, we have no availability for an appointment."
msgstr "Beklager, vi har ingen ledige tider."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Sorry, we have no more slots available for this month."
msgstr "Beklager, vi har ikke flere ledige tider i denne måned."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__source_resource_ids
msgid "Source combination"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__appointment_resource_ids
msgid "Specific Resources"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_invite__resources_choice__specific_resources
msgid "Specific Users/Resources"
msgstr ""

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_view_bookings_users
#: model:ir.actions.server,name:appointment.calendar_event_action_all_users_appointments
#: model:ir.ui.menu,name:appointment.menu_appointment_schedule_staff_appointment
msgid "Staff Bookings"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_manage_leaves__leave_start_dt
msgid "Start Date"
msgstr "Start dato"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__start_datetime
msgid "Start Datetime"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__event_start
msgid "Start date of an event, without time for full days events"
msgstr ""
"Start dato på et arrangement, uden at sætte tid, hvis det er et "
"heldagsarrangement"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_slot__start_datetime
msgid "Start datetime for unique slot type management"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__start_hour
msgid "Starting Hour"
msgstr "Start time"

#. module: appointment
#: model:onboarding.onboarding.step,done_text:appointment.appointment_onboarding_configure_calendar_provider_step
#: model:onboarding.onboarding.step,done_text:appointment.appointment_onboarding_preview_invite_step
msgid "Step Completed!"
msgstr "Trin gennemført!"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_booking_line__event_stop
msgid "Stop date of an event, without time for full days events"
msgstr ""
"Slutdato på et arrangement, uden at sætte tid, hvis det er et "
"heldagsarrangement"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Subject"
msgstr "Emne"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_question__answer_input_ids
msgid "Submitted Answers"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__7
msgid "Sunday"
msgstr "Søndag"

#. module: appointment
#: model:appointment.question,name:appointment.appointment_type_dental_care_question_1
msgid "Symptoms"
msgstr "Symptomer"

#. module: appointment
#: model:appointment.type,name:appointment.appointment_type_tennis_court
msgid "Tennis Court"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_answer_input__value_text_box
msgid "Text Answer"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_answer_input_view_search
msgid "Text Questions"
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_invite_short_code_uniq
#: model_terms:ir.ui.view,arch_db:appointment.appointment_invite_view_form
msgid "The URL is already taken, please pick another code."
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_type_check_resource_manual_confirmation_percentage
msgid "The capacity percentage should be between 0 and 100%"
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_booking_line_check_capacity_reserved
msgid "The capacity reserved should be positive."
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_booking_line_check_capacity_used
msgid "The capacity used can not be lesser than the capacity reserved"
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_slot_check_start_and_end_hour
msgid "The end time must be later than the start time."
msgstr "Sluttidspunkter skal være efter starttidspunktet."

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "The event %s cannot book resources without an appointment type."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "The field '%s' does not exist in the targeted model"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_invite.py:0
#, python-format
msgid "The following appointment type(s) have no resource assigned: %s."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_invite.py:0
#, python-format
msgid "The following appointment type(s) have no staff assigned: %s."
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_question.py:0
#, python-format
msgid "The following question(s) do not have any selectable answers : %s"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/appointment_type.py:0
#, python-format
msgid ""
"The following users are in restricted slots but they are not part of the "
"available staff: %s"
msgstr ""

#. module: appointment
#: model:ir.model.constraint,message:appointment.constraint_appointment_resource_check_capacity
msgid "The resource should have at least one capacity."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__sequence
msgid ""
"The sequence dictates if the resource is going to be picked in higher priority against another resource\n"
"        (e.g. for 2 tables of 4, the lowest sequence will be picked first)"
msgstr ""

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "Their first availability is"
msgstr "Deres første ledige tid er"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "There is currently no appointment available"
msgstr "Der er i øjeblikket ingen ledige tider"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "There is no appointment linked to your account."
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__shareable
msgid ""
"This allows to share the resource with multiple attendee for a same time "
"slot (e.g. a bar counter)"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it does not have any "
"opening hours configured"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no resource "
"assigned"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no resource "
"assigned and does not have any opening hours configured"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no staff assigned"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"This appointment type has no availabilities because it has no staff assigned"
" and does not have any opening hours configured"
msgstr ""

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Dette felt bruges til at definere i hvilken tidszone ressourcerne vil "
"arbejde."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_edit_in_backend
msgid "This is a preview of the customer appointment form."
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__4
msgid "Thursday"
msgstr "Torsdag"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__tz
#: model:ir.model.fields,field_description:appointment.field_appointment_type__appointment_tz
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Timezone"
msgstr "Tidszone"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__appointment_tz
msgid "Timezone where appointment take place"
msgstr "Tidszone hvor aftale tager sted"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Timezone:"
msgstr "Tidszone:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "To"
msgstr "Til"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__access_token
msgid "Token"
msgstr "Token"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_kanban
msgid "Total"
msgstr "I alt"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__resource_total_capacity
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_tree
msgid "Total Capacity"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resource_total_capacity_reserved
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Total Capacity Reserved"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__resource_total_capacity_used
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_tree_booking
msgid "Total Capacity Used"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "Total Reserved"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Total:"
msgstr "Total:"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__2
msgid "Tuesday"
msgstr "Tirsdag"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_search
msgid "Type"
msgstr "Type"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Uncertain"
msgstr "Usikkert"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/views/gantt/gantt_popover.xml:0
#, python-format
msgid "Unconfirm Check-In"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Until"
msgstr "Indtil"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "Until (max)"
msgstr "Indtil (max)"

#. module: appointment
#. odoo-python
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Upcoming"
msgstr "Kommende"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_alarm__default_for_new_appointment_type
msgid "Use as default for new Appointment Types"
msgstr "Anvend som standard for nye aftaletyper"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__category
msgid ""
"Used to define this appointment type's category.\n"
"\n"
"        Can be one of:\n"
"\n"
"            - Recurring: the default category, weekly recurring slots. Accessible from the website\n"
"\n"
"            - Punctual: recurring slots limited between 2 datetimes. Accessible from the website\n"
"\n"
"            - Custom: the user will create and share to another user a custom appointment type with hand-picked time slots\n"
"\n"
"            - Anytime: the user will create and share to another user an appointment type covering all their time slots"
msgstr ""

#. module: appointment
#: model:res.groups,name:appointment.group_appointment_user
msgid "User"
msgstr "Bruger"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_invite__staff_user_ids
#: model:ir.model.fields,field_description:appointment.field_appointment_type__staff_user_ids
#: model:ir.model.fields.selection,name:appointment.selection__appointment_type__schedule_based_on__users
msgid "Users"
msgstr "Brugere"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__event_videocall_source
msgid "Videoconference Link"
msgstr "Link til videokonference"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_type__website_message_ids
msgid "Website Messages"
msgstr "Beskeder fra hjemmesiden"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_type__website_message_ids
msgid "Website communication history"
msgstr "Website kommunikations historik"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__appointment_slot__weekday__3
msgid "Wednesday"
msgstr "Onsdag"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_slot__weekday
msgid "Week Day"
msgstr "Ugedag"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_appointment_resource__resource_calendar_id
msgid ""
"If kept empty, the working schedule of the company set on the resource will "
"be used"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.staff_user_select
msgid "With"
msgstr "Med"

#. module: appointment
#: model:onboarding.onboarding.step,description:appointment.appointment_onboarding_configure_calendar_provider_step
msgid "With Outlook or Google"
msgstr "Med Outlook eller Google"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_appointment_resource__resource_calendar_id
msgid "Working Hours"
msgstr "Arbejdstimer"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/js/appointment_form.js:0
#: code:addons/appointment/static/src/js/appointment_validation.js:0
#, python-format
msgid "You cannot invite more than 10 people"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_details_column
msgid "Your Appointment"
msgstr "Din aftale"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"Your appointment has been reserved! We will come back to you to confirm it."
msgstr ""
"Din aftale er reserveret! Vi vender tilbage til dig for at bekræfte den."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Your appointment has successfully been booked!"
msgstr "Din aftale er blevet booket med succes!"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Your appointment is in less than"
msgstr "Din aftale er om mindre end"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_user
msgid "Your choice"
msgstr "Dit valg"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "days"
msgstr "dage"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"During this meeting, we will...\""
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"I feel nauseous...\""
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_form_gantt_booking
msgid "e.g. \"John Doe - Tennis Court Booking\""
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"Technical Demo\""
msgstr "fx. \"Teknisk demo\""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"Thank you for your trust, we look forward to meeting you!\""
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "e.g. \"What are your symptoms?\""
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "e.g. +1(605)691-3277"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_manage_leaves_view_form
msgid "e.g. Inventory count and valuation"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "e.g. John Smith"
msgstr "fx John Smith"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_resource_view_form
msgid "e.g. Tennis Court 1"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"e.g. <EMAIL>\r\n"
"e.g. <EMAIL>\r\n"
"..."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid ""
"e.g. <EMAIL> \r\n"
"e.g. <EMAIL>\r\n"
"..."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "e.g. <EMAIL>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "from"
msgstr "fra"

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "has no availability for an appointment."
msgstr "har ingen ledige tider."

#. module: appointment
#. odoo-javascript
#: code:addons/appointment/static/src/xml/appointment_no_slot.xml:0
#, python-format
msgid "has no more slots available for this month."
msgstr "har ikke flere ledige tider denne måned."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "hours before"
msgstr "timer før"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "hours from now!"
msgstr "timer fra nu!"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_meeting_details
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "people"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "persons)"
msgstr ""

#. module: appointment
#. odoo-python
#: code:addons/appointment/models/calendar_event.py:0
#, python-format
msgid "somebody"
msgstr ""

#. module: appointment
#: model:onboarding.onboarding.step,description:appointment.appointment_onboarding_create_appointment_type_step
msgid "to automate appointments"
msgstr "for at automatisere aftaler"

#. module: appointment
#: model:onboarding.onboarding.step,description:appointment.appointment_onboarding_preview_invite_step
msgid "to schedule appointments"
msgstr "for at planlægge aftaler"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "total capacity"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_view_form
msgid "when over"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "with"
msgstr "med"
