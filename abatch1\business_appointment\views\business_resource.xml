<?xml version="1.0"?>
<odoo>

    <record id="business_resource_view_search" model="ir.ui.view">
        <field name="name">business.resource.search</field>
        <field name="model">business.resource</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="description"/>
                <field name="resource_type_id"/>
                <field name="user_id"/>
                <field name="resource_calendar_id"/>
                <field name="final_service_ids"/>

                <filter string="My" name="my_resources" domain="[('user_id', '=', uid)]"/>
                <filter string="Unassigned" name="unassigned_resources" domain="[('user_id', '=', False)]"/>
                <separator/>
                <filter string="Activities Todo" name="activities_my" domain="[('activity_ids.user_id', '=', uid)]"/>
                <separator/>
                <filter string="Late Activities"
                        name="activities_overdue"
                        domain="[('activity_ids.date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                />
                <filter string="Today Activities"
                        name="activities_today"
                        domain="[('activity_ids.date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"
                />
                <filter string="Future Activities"
                        name="activities_upcoming_all"
                        domain="[('activity_ids.date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"
                />
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Resource Type" name="group_resource_type_id" context="{'group_by': 'resource_type_id'}"/>
                    <filter string="Responsible" name="group_resource_user_id" context="{'group_by': 'user_id'}"/>
                    <filter string="Resource Kind" name="group_resource_type" context="{'group_by': 'resource_type'}"/>
                    <filter string="Working Calendar" name="group_resource_calendar_id" context="{'group_by': 'resource_calendar_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="business_resource_view_form" model="ir.ui.view">
        <field name="name">business.resource.form</field>
        <field name="model">business.resource</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(business_appointment.business_appointment_action)d"
                                type="action"
                                class="oe_stat_button"
                                icon="fa-calendar-check-o"
                                context="{'default_resource_id': active_id, 'default_resource_type_id': resource_type_id}"
                        >
                            <field string="Appointments" name="planned_appointment_len" widget="statinfo"/>
                        </button>
                        <button name="action_open_leaves"
                                type="object"
                                class="oe_stat_button"
                                icon="fa-calendar"
                        >
                            Own Leaves
                        </button>
                        <button name="%(business_appointment.rating_rating_action_business_resource)d" 
                                type="action" 
                                attrs="{'invisible': [('rating_satisfaction', '=', -1)]}" 
                                class="oe_stat_button oe_percent" 
                                icon="fa-smile-o" 
                                groups="business_appointment.group_business_appointment_rating"
                        >
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">
                                    <field name="rating_satisfaction" nolabel="1"/>
                                </span>
                                <span class="o_stat_text">%</span>
                            </div>
                        </button>
                    </div>
                    <field name="image_1920"
                           widget="image"
                           class="oe_avatar oe_left"
                           options="{'preview_image': 'image_128'}"
                    />
                    <div class="oe_title">
                        <h1>
                            <field name="name"
                                   placeholder="resource reference"
                                   class="oe_inline"
                                   required="1"
                            />
                        </h1>
                    </div>
                    <group name="ba_resource_details_group">
                        <group>
                            <field name="resource_type" invisible="1"/>
                            <field name="resource_type_id"/>
                            <field name="service_ids"
                                   widget="many2many_tags"
                                   options="{'no_create_edit': 1, 'no_quick_create': 1}"
                                   domain="[('id', 'in', type_available_service_ids)]"
                                   attrs="{'invisible': [('service_method', '!=', 'multiple')], 'required': [('service_method', '=', 'multiple')]}"
                            />
                            <field name="active" invisible="1" />
                        </group>
                        <group>
                            <field name="resource_calendar_id" required="1"/>
                            <field name="user_id"
                                   domain="[('share', '=', False)]"
                                   options="{'no_create_edit': 1, 'no_quick_create': 1}"
                            />
                            <field name="location"/>
                            <field name="sucess_email_partner_ids"
                                   widget="many2many_tags"
                                   options="{'no_create_edit': 1, 'no_quick_create': 1}"
                            />
                        </group>
                    </group>
                    <group invisible="1">
                        <field name="color"/>
                        <field name="tz"/>
                        <field name="service_method"/>
                        <field name="type_available_service_ids"
                               widget="many2many_tags"
                               options="{'no_create_edit': 1, 'no_quick_create': 1}"
                        />
                        <field name="company_id"
                               options="{'no_create_edit': 1, 'no_quick_create': 1}"
                               groups="base.group_multi_company"
                        />
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description" placeholder="short description" />
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
    <record  id="business_resource_view_kanban" model="ir.ui.view" >
        <field name="name">business.resource.kanban</field>
        <field name="model">business.resource</field>
        <field name="arch" type="xml">
            <kanban default_group_by='resource_type_id' class="oe_background_grey o_kanban_dashboard ba_kanban_main"
                    group_create="0" group_delete="0" group_edit="0" archivable="0" quick_create="0">
                <field name="id"/>
                <field name="name"/>
                <field name="user_id"/>
                <field name="color"/>
                <field name="image_128"/>
                <field name="resource_type_id"/>
                <field name="resource_calendar_id"/>
                <field name="activity_ids"/>
                <field name="activity_state"/>
                <field name="final_service_ids"/>
                <field name="planned_appointment_len"/>
                <field name="rating_satisfaction"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{kanban_color(record.color.raw_value)} oe_kanban_global_click">
                            <div>
                                <div class="o_kanban_card_content ba_kanban_content">
                                    <div class="o_kanban_image">
                                        <a type="open">
                                            <img t-att-src="kanban_image('business.resource', 'image_128', record.id.value)"
                                                 class="o_image_64_contain"
                                                 alt="Resource Image"
                                            />
                                        </a>
                                    </div>
                                    <div class="o_kanban_primary_left ba_kanban_primary_left">
                                        <div class="o_primary ba_primary">
                                            <span><t t-out="record.name.value"/></span>
                                        </div>
                                    </div>
                                    <div class="oe_kanban_details ba_kanban_details">
                                        <ul>
                                            <li><span><field name="resource_type_id"/></span></li>
                                            <li><span><field name="final_service_ids" widget="many2many_tags"/></span></li>
                                            <li><span><field name="resource_calendar_id"/></span></li>
                                            <li>
                                                <a type="object" name="action_open_appointments">
                                                    <span>
                                                        <field name="planned_appointment_len"/> Appointments
                                                    </span>
                                                </a>
                                            </li>
                                            <li attrs="{'invisible': [('rating_satisfaction', '=', -1)]}"
                                                groups="business_appointment.group_business_appointment_rating"
                                            >
                                                <a name="%(business_appointment.rating_rating_action_business_resource)d" type="action">
                                                    <i class="fa fa-smile-o" 
                                                       role="img"
                                                       aria-label="Percentage of satisfaction"
                                                       title="Percentage of satisfaction"/> 
                                                    <t t-out="record.rating_satisfaction.value"/> %
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="o_kanban_card_manage_pane dropdown-menu" role="menu">
                                    <div class="o_kanban_card_manage_section o_kanban_manage_reports">
                                        <div role="menuitem">
                                            <a type="edit">Edit</a>
                                        </div>
                                        <div role="menuitem">
                                            <a type="delete">Delete</a>
                                        </div>
                                        <div role="menuitem" aria-haspopup="true" class="o_no_padding_kanban_colorpicker">
                                            <ul class="oe_kanban_colorpicker" data-field="color" role="popup"/>
                                        </div>
                                    </div>
                                </div>
                                <a class="o_kanban_manage_toggle_button o_left" href="#">
                                    <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                               </a>
                               <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <div class="o_kanban_inline_block">
                                            <field name="activity_ids" widget="kanban_activity"/>
                                        </div>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <img t-att-src="kanban_image('res.users', 'image_128', record.user_id.raw_value)"
                                             t-att-title="record.user_id.value"
                                             t-att-alt="record.user_id.value"
                                             width="24"
                                             height="24"
                                             class="oe_kanban_avatar float-right"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="business_resource_view_tree" model="ir.ui.view">
        <field name="name">business.resource.tree</field>
        <field name="model">business.resource</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle"/>
                <field name='name'/>
                <field name="resource_type_id"/>
                <field name="final_service_ids" widget="many2many_tags" options="{'no_create_edit': 1, 'no_quick_create': 1}"/>
                <field name="resource_calendar_id"/>
            </tree>
        </field>
    </record>
    <record id="business_resource_action" model="ir.actions.act_window">
        <field name="name">Resources</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">business.resource</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" eval="business_resource_view_search"/>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                Click 'Create' to add new resource, e.g. 'Doctor Brown', 'Room 212', 'Milling machine 909'
            </p>
        </field>
    </record>

</odoo>
