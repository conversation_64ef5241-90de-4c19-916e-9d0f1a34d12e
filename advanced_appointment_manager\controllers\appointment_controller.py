# -*- coding: utf-8 -*-
from odoo import http
from odoo.http import request
from odoo.addons.website_appointment.controllers.appointment import WebsiteAppointment
import logging

_logger = logging.getLogger(__name__)

class AdvancedAppointmentController(WebsiteAppointment):

    @http.route(['/appointment/<model("appointment.type"):appointment_type>/submit'], type='http', auth="public", website=True, methods=['POST'])
    def make_appointment(self, appointment_type, **kwargs):
        
        # This log message is the key diagnostic test.
        _logger.info("CUSTOM APPOINTMENT CONTROLLER: make_appointment method has been called.")
        
        event = super().make_appointment(appointment_type=appointment_type, **kwargs)

        # Render your custom "pending approval" template
        return request.render('advanced_appointment_manager.appointment_pending_template', {
            'event': self.env['calendar.event'].browse(event.id)
        })

    @http.route('/appointment/pending', type='http', auth='public', website=True)
    def appointment_pending(self, **kwargs):
        return request.render('advanced_appointment_manager.appointment_pending_template', {'event': False})