<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="portal_my_home_appointment" name="Portal My Home: Appointments" inherit_id="portal.portal_my_home" priority="40">
            <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
                <t t-if="appointment_count" class="col-12 col-lg-6">
                    <a class="text-decoration-none" href="/my/appointments">
                        <div class="card bg-white h-100 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <h4 class="card-title text-primary mb-1">Appointments</h4>
                                        <p class="card-text text-muted mb-0">View your upcoming appointments.</p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span t-out="appointment_count" class="badge rounded-pill bg-primary fs-3"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                </t>
            </xpath>
        </template>
        <template id="portal_my_appointments" name="My Appointments">
            <t t-call="portal.portal_layout">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="mb-0">Your Appointments</h3>
                    <a href="/appointment" class="btn btn-primary">Book a New Appointment</a>
                </div>
                <t t-if="not appointments"><div class="alert alert-info" role="alert">You currently have no appointments.</div></t>
                <t t-if="appointments">
                    <div class="table-responsive">
                        <table class="table table-hover o_portal_my_doc_table">
                            <thead><tr class="table-light"><th>Subject</th><th>Date &amp; Time</th><th class="text-center">Status</th></tr></thead>
                            <tbody>
                                <t t-foreach="appointments" t-as="appt">
                                    <tr>
                                        <td><a t-attf-href="/my/appointments/#{appt.id}"><t t-out="appt.name"/></a></td>
                                        <td><span t-field="appt.start" t-options="{'widget': 'datetime'}"/></td>
                                        <td class="text-center">
                                            <span t-if="appt.approval_state == 'pending'" class="badge rounded-pill text-bg-warning">Pending Approval</span>
                                            <span t-if="appt.approval_state == 'approved'" class="badge rounded-pill text-bg-success">Approved</span>
                                            <span t-if="appt.approval_state == 'rejected'" class="badge rounded-pill text-bg-danger">Rejected</span>
                                            <span t-if="appt.approval_state == 'cancelled'" class="badge rounded-pill text-bg-secondary">Cancelled by You</span>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </t>
                <div t-if="pager" class="o_portal_pager text-center"><t t-call="portal.pager"/></div>
            </t>
        </template>
        <template id="portal_appointment_detail" name="Appointment Detail">
            <t t-call="portal.portal_layout">
                <t t-set="card_header">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <h5 class="my-0"><span t-field="appointment.name"/></h5>
                        <div class="mt-2 mt-md-0">
                            <span t-if="appointment.approval_state == 'pending'" class="badge rounded-pill text-bg-warning fs-6">Pending Approval</span>
                            <span t-if="appointment.approval_state == 'approved'" class="badge rounded-pill text-bg-success fs-6">Approved</span>
                            <span t-if="appointment.approval_state == 'rejected'" class="badge rounded-pill text-bg-danger fs-6">Rejected</span>
                            <span t-if="appointment.approval_state == 'cancelled'" class="badge rounded-pill text-bg-secondary fs-6">Cancelled by You</span>
                        </div>
                    </div>
                </t>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="mb-3"><strong>Date:</strong><p class="ms-2"><span t-field="appointment.start" t-options="{'widget': 'datetime'}"/></p></div>
                            <div class="mb-3"><strong>Duration:</strong><p class="ms-2"><span t-esc="'%.2f' % appointment.duration"/> hours</p></div>
                            <div class="mb-3"><strong>Appointment Type:</strong><p class="ms-2"><span t-field="appointment.appointment_type_id.name"/></p></div>
                            <div class="mb-3" t-if="appointment.location"><strong>Location:</strong><p class="ms-2"><span t-field="appointment.location"/></p></div>
                        </div>
                        <div class="col-lg-4 text-lg-end">
                            <t t-if="appointment.approval_state in ['pending', 'approved'] and appointment.start > now">
                                 <form t-attf-action="/my/appointments/#{appointment.id}/cancel" method="POST">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to cancel this appointment?')"><i class="fa fa-times me-1"/>Cancel Appointment</button>
                                </form>
                                <p class="text-muted small mt-2">Need a different time? Cancel this appointment and <a href="/appointment">book a new one</a>.</p>
                            </t>
                            <t t-if="appointment.start &lt; now and appointment.approval_state not in ['cancelled', 'rejected']"><p class="text-muted">This appointment has already occurred.</p></t>
                        </div>
                    </div>
                </div>
            </t>
        </template>
    </data>
</odoo>