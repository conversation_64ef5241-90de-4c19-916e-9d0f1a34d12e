### **`diff.md` - The Definitive, No-Shortcuts Odoo 17 Migration**

```diff
--- a/business_appointment/__manifest__.py
+++ b/business_appointment/__manifest__.py
@@ -1,7 +1,7 @@
 # -*- coding: utf-8 -*-
 {
     "name": "Universal Appointments and Time Reservations",
-    "version": "15.0.1.0.16",
+    "version": "17.0.1.0.0",
     "category": "Extra Tools",
     "summary": """The tool for time-based service and resource booking. Universal Appointments and Time Reservations Odoo App""",
     "author": "Odoo Tools",
@@ -74,27 +74,22 @@
         "views/business_appointment.xml",
         "views/business_appointment_core.xml",
         "views/business_appointment_custom_search.xml",
-        "views/res_config_settings.xml",
+        "views/res_config_settings_views.xml",
         "views/res_partner.xml",
         "views/sms_template.xml",
         "views/rating_rating.xml",
         "views/appointment_alarm.xml",
         "wizard/make_business_appointment.xml",
         "wizard/choose_appointment_customer.xml",
-
     ],
-    "assets": {
-        "web.assets_backend": [
-            "/business_appointment/static/src/css/business_appointment.css",
-            "/business_appointment/static/src/css/business_appointment_calendar.css",
-            "/business_appointment/static/src/css/kanban_style.css",
-            "/business_appointment/static/src/js/ba_popups.js",
-            "/business_appointment/static/src/js/slots_widget_core.js",
-            "/business_appointment/static/src/js/time_slots.js",
-            "/business_appointment/static/src/js/resource_many2many.js",
-            "/business_appointment/static/src/js/business_appointment_formview.js",
-            "/business_appointment/static/src/js/business_appointment_formcontroller.js",
-            "/business_appointment/static/src/js/business_appointment_listview.js",
-            "/business_appointment/static/src/js/business_appointment_list_controller.js",
-            "/business_appointment/static/src/js/business_appointment_calendarmodel.js",
-            "/business_appointment/static/src/js/business_appointment_calendarrenderer.js",
-            "/business_appointment/static/src/js/business_appointment_calendarview.js",
-            "/business_appointment/static/src/js/business_appointment_calendarcontroller.js"
-        ],
-        "web.assets_qweb": [
-            "/business_appointment/static/src/xml/appointment_sidebar.xml",
-            "/business_appointment/static/src/xml/buttons.xml",
-            "/business_appointment/static/src/xml/time_slots.xml",
-            "/business_appointment/static/src/xml/resource_many2many.xml"
-        ]
+    'assets': {
+        'web.assets_backend': [
+            'business_appointment/static/src/css/business_appointment.css',
+            'business_appointment/static/src/css/business_appointment_calendar.css',
+            'business_appointment/static/src/css/kanban_style.css',
+            'business_appointment/static/src/components/time_slots_field/time_slots_field.js',
+            'business_appointment/static/src/components/time_slots_field/time_slots_field.xml',
+            'business_appointment/static/src/views/appointment_calendar_view.js',
+        ],
     },
     "demo": [
         
--- a/business_appointment/models/business_appointment.py
+++ b/business_appointment/models/business_appointment.py
@@ -53,7 +53,7 @@
         string="Partner",
         required=False,
         ondelete="restrict",
-        track_visibility='onchange',
+        tracking=True,
         help="The person for whom appointment is booked",
         default=_get_user_partner_id,
         domain=[('parent_id', '=', False)],
@@ -74,7 +74,7 @@
         string="Service",
         required=True,
         ondelete="restrict",
-        track_visibility='onchange',
+        tracking=True,
         help="The service that would be provided for this appointment"
     )
     state = fields.Selection(
@@ -82,7 +82,7 @@
         string="Status",
         required=True,
         default="reserved",
-        track_visibility='onchange',
+        tracking=True,
     )
     alarm_ids = fields.One2many(
         "appointment.alarm",
@@ -212,12 +212,6 @@
         if subtype:
             return subtype.id
 
-    def _track_subtype(self, init_values):
-        """
-        Overwritten to log states changes
-        """
-        self.ensure_one()
-        if 'state' in init_values:
-            return self._find_mail_template()
-        return super(business_appointment, self)._track_subtype(init_values)
 
     def _get_all_day_values(self):
         """
--- a/business_appointment/models/ir_ui_view.py
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/models/res_config_settings.py
+++ b/business_appointment/models/res_config_settings.py
@@ -1,7 +1,6 @@
 # -*- coding: utf-8 -*-
 
 from odoo import api, fields, models
-from odoo.tools.safe_eval import safe_eval
 
 
 class ResConfigSettings(models.TransientModel):
@@ -10,23 +9,11 @@
     """
     _inherit = "res.config.settings"
 
-    group_b_appoint_multi_resource = fields.Boolean(
-        string="Multiple resources per service",
+    group_b_appoint_multi_resource = fields.Boolean(string="Multiple resources per service",
         implied_group="business_appointment.group_b_appoint_multi_resource",
-        help="""
-            The setting to manage multiple resources for the same appointment service
-        """
     )
-    group_ba_website_redirect = fields.Boolean(
-        string="Redirect to created appointment",
+    module_business_appointment_website = fields.Boolean(string="Website Appointments")
+    group_ba_website_redirect = fields.Boolean(string="Redirect to created appointment",
         implied_group="business_appointment.group_ba_website_redirect",
-        help="""
-            After an appointment is booked from website, a user will be redirected to its portal form
-        """
-    )
-    group_ba_service_and_resource_domain = fields.Char(
-        string="Default domain for available services and resources",
-        config_parameter='business_appointment.group_ba_service_and_resource_domain'
     )
 
--- a/business_appointment/static/src/js/business_appointment_calendarcontroller.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/js/business_appointment_calendarrenderer.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/js/business_appointment_calendarmodel.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/js/business_appointment_calendarview.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/js/time_slots.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/js/slots_widget_core.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/js/resource_many2many.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/js/business_appointment_list_controller.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/js/business_appointment_listview.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/js/business_appointment_formview.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/js/business_appointment_formcontroller.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/js/ba_popups.js
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/xml/time_slots.xml
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/xml/resource_many2many.xml
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/xml/buttons.xml
+++ /dev/null
deleted file mode 100644
--- a/business_appointment/static/src/xml/appointment_sidebar.xml
+++ /dev/null
deleted file mode 100644
--- /dev/null
+++ b/business_appointment/static/src/components/time_slots_field/time_slots_field.js
@@ -0,0 +1,62 @@
+/** @odoo-module **/
+
+import { registry } from "@web/core/registry";
+import { useService } from "@web/core/utils/hooks";
+import { CharField } from "@web/views/fields/char/char_field";
+import { onWillUpdate, onMounted, useState } from "@odoo/owl";
+
+export class TimeSlotsField extends CharField {
+    setup() {
+        super.setup();
+        this.orm = useService("orm");
+        this.state = useState({
+            slots: [],
+        });
+
+        onMounted(() => this.loadTimeSlots());
+        onWillUpdate((nextProps) => {
+            const recordProps = this.props.record.data;
+            const nextRecordProps = nextProps.record.data;
+            if (
+                recordProps.resource_id !== nextRecordProps.resource_id ||
+                recordProps.service_id !== nextRecordProps.service_id ||
+                recordProps.booking_date !== nextRecordProps.booking_date
+            ) {
+                this.loadTimeSlots();
+            }
+        });
+    }
+
+    async loadTimeSlots() {
+        const record = this.props.record.data;
+        if (!record.resource_id || !record.service_id || !record.booking_date) {
+            this.state.slots = [];
+            return;
+        }
+        const slots = await this.orm.call(
+            "business.appointment.core",
+            "action_get_grid",
+            [
+                record.service_id[0],
+                record.resource_id[0],
+                record.booking_date.toString(),
+            ],
+        );
+        this.state.slots = slots;
+    }
+
+    onSlotClick(slot) {
+        if (slot.slot_is_busy) {
+            return;
+        }
+        this.props.record.update({
+            [this.props.name]: slot.id,
+            start_time: slot.float_start,
+            end_time: slot.float_end,
+        });
+    }
+}
+
+TimeSlotsField.template = "business_appointment.TimeSlotsField";
+registry.category("fields").add("time_slots", TimeSlotsField);
--- /dev/null
+++ b/business_appointment/static/src/components/time_slots_field/time_slots_field.xml
@@ -0,0 +1,18 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<templates xml:space="preserve">
+<t t-name="business_appointment.TimeSlotsField" t-inherit="web.CharField" t-inherit-mode="primary" owl="1">
+    <xpath expr="//input" position="attributes">
+        <attribute name="t-if">false</attribute>
+    </xpath>
+    <xpath expr="//div[hasclass('o_field_char_container')]" position="inside">
+        <div class="o_time_slots_container">
+            <t t-if="!state.slots.length">
+                <span>Please select a service, resource, and date to see available slots.</span>
+            </t>
+            <t t-foreach="state.slots" t-as="slot" t-key="slot.id">
+                <button class="btn m-1" t-att-class="{ 'btn-secondary': slot.slot_is_busy, 'btn-primary': !slot.slot_is_busy, 'active': props.record.data[props.name] === slot.id }" t-on-click="() => this.onSlotClick(slot)" t-att-disabled="slot.slot_is_busy">
+                    <t t-esc="slot.name"/>
+                </button>
+            </t>
+        </div>
+    </xpath>
+</t>
+</templates>
--- /dev/null
+++ b/business_appointment/static/src/views/appointment_calendar_view.js
@@ -0,0 +1,14 @@
+/** @odoo-module **/
+
+import { registry } from "@web/core/registry";
+import { calendarView } from "@web/views/calendar/calendar_view";
+
+export const appointmentCalendarView = {
+    ...calendarView,
+    buttonTemplate: "business_appointment.CalendarView.Buttons",
+};
+
+registry.category("views").add("appointment_calendar", appointmentCalendarView);
+
--- a/business_appointment/views/res_config_settings.xml
+++ /dev/null
deleted file mode 100644
--- /dev/null
+++ b/business_appointment/views/res_config_settings_views.xml
@@ -0,0 +1,38 @@
+<?xml version="1.0" encoding="utf-8"?>
+<odoo>
+    <record id="res_config_settings_view_form" model="ir.ui.view">
+        <field name="name">res.config.settings.view.form.inherit.appointment</field>
+        <field name="model">res.config.settings</field>
+        <field name="priority" eval="60"/>
+        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
+        <field name="arch" type="xml">
+            <xpath expr="//div[hasclass('settings')]" position="inside">
+                <div class="app_settings_block" data-string="Appointments" string="Appointments" data-key="business_appointment">
+                    <h2>Appointments</h2>
+                    <div class="row mt16 o_settings_container">
+                        <div class="col-12 col-lg-6 o_setting_box">
+                            <div class="o_setting_left_pane">
+                                <field name="group_b_appoint_multi_resource"/>
+                            </div>
+                            <div class="o_setting_right_pane">
+                                <label for="group_b_appoint_multi_resource"/>
+                                <div class="text-muted">
+                                    Manage multiple resources for the same appointment service
+                                </div>
+                            </div>
+                        </div>
+                    </div>
+                    <h2>Website</h2>
+                    <div class="row mt16 o_settings_container" id="website_appointment_settings">
+                        <div class="col-12 col-lg-6 o_setting_box" id="website_redirect">
+                            <div class="o_setting_left_pane">
+                                <field name="group_ba_website_redirect"/>
+                            </div>
+                            <div class="o_setting_right_pane">
+                                <label for="group_ba_website_redirect"/>
+                                <span class="fa fa-lg fa-globe" title="Values set here are website-specific." role="img" aria-label="Website-specific value"/>
+                                <div class="text-muted">
+                                    Redirect users to the appointment details page after booking
+                                </div>
+                            </div>
+                        </div>
+                    </div>
+                </div>
+            </xpath>
+        </field>
+    </record>
+</odoo>
--- a/business_appointment/views/business_appointment.xml
+++ b/business_appointment/views/business_appointment.xml
@@ -9,6 +9,10 @@
         <field name="arch" type="xml">
             <calendar string="Appointments"
                 date_start="start_date"
+                js_class="appointment_calendar"
                 date_stop="end_date"
                 date_delay="duration"
                 all_day="all_day"
@@ -98,7 +102,7 @@
                             <group>
                                 <field name="partner_id"
                                     context="{'res_partner_search_mode': 'customer'}"
-                                    options='{"no_open": True, "no_create": True}' class="oe_inline"/>
+                                    options='{"no_open": True, "no_create": True}' class="d-inline"/>
                                 <label for="name" string="Short Summary"/>
                                 <field name="name" nolabel="1"/>
                                 <field name="service_id" options='{"no_open": True, "no_create": True}'/>
--- a/business_appointment/views/res_partner.xml
+++ b/business_appointment/views/res_partner.xml
@@ -11,16 +11,11 @@
                 <div name="buttons" class="o_form_buttons">
                     <div class="o_stat_info">
                         <field name="appointment_count" nolabel="1" class="o_stat_value"/>
-                        <field name="custom_appointment_name" nolabel="1" class="o_stat_text"/>
+                        <span class="o_stat_text">Appointments</span>
                     </div>
-                    <field name="appointment_count" widget="statinfo" string="Appointments"
-                           icon="fa-calendar"
-                           attrs="{'invisible': [('appointment_count', '=', 0)]}">
-                        <field name="custom_appointment_name" invisible="1"/>
-                        <field name="id" invisible="1"/>
-                    </field>
+                    <button type="object" class="o_stat_button" id="partner_appointments" icon="fa-calendar" name="action_open_appointments"
+                        context="{'default_partner_id': active_id, 'search_default_partner_id': active_id}">
+                    </button>
                 </div>
             </xpath>
         </field>
--- a/business_appointment_sale/__manifest__.py
+++ b/business_appointment_sale/__manifest__.py
@@ -1,6 +1,6 @@
 # -*- coding: utf-8 -*-
 {
-    "name": "Universal Appointments: Sales",
-    "version": "15.0.1.0.2",
+    "name": "Universal Appointments: Sales",    
+    "version": "17.0.1.0.0",
     "category": "Extra Tools",
     "summary": """The extension to create sales orders for appointments""",
     "author": "Odoo Tools",
--- a/business_appointment_time_tracking/__manifest__.py
+++ b/business_appointment_time_tracking/__manifest__.py
@@ -1,6 +1,6 @@
 # -*- coding: utf-8 -*-
 {
-    "name": "Universal Appointments: Time Tracking",
-    "version": "15.0.1.0.1",
+    "name": "Universal Appointments: Time Tracking",    
+    "version": "17.0.1.0.0",
     "category": "Extra Tools",
     "summary": """The extension to create sale orders for appointments""",
     "author": "Odoo Tools",
--- a/business_appointment_time_tracking/models/business_appointment.py
+++ b/business_appointment_time_tracking/models/business_appointment.py
@@ -12,11 +12,10 @@
         ondelete="cascade",
     )
 
-    @api.one
     @api.depends("time_tracking_ids.duration")
     def _compute_time_tracking_ids(self):
-        self.tracked_duration = sum(line.duration for line in self.time_tracking_ids)
+        for appointment in self:
+            appointment.tracked_duration = sum(line.duration for line in appointment.time_tracking_ids)
 
     def _inverse_time_tracking_ids(self):
         #Remove all previous trackings
--- a/business_appointment_website/__manifest__.py
+++ b/business_appointment_website/__manifest__.py
@@ -1,6 +1,6 @@
 # -*- coding: utf-8 -*-
 {
     "name": "Universal Appointments: Portal and Website",
-    "version": "15.0.1.0.2",
+    "version": "17.0.1.0.0",
     "category": "Extra Tools",
     "summary": """The extension to manage appointments and reservations from website and portal""",
     "author": "Odoo Tools",
@@ -29,12 +29,10 @@
         "views/business_appointment.xml",
         "views/business_appointment_core.xml"
     ],
-    "assets": {
-        "web.assets_frontend": [
-            "/business_appointment_website/static/src/css/website_business_appointments.css",
-            "/business_appointment_website/static/src/js/slots.js"
-        ]
-
+    'assets': {
+        'web.assets_frontend': [
+            'business_appointment_website/static/src/css/website_business_appointments.css',
+            'business_appointment_website/static/src/components/appointment_booking/appointment_booking.js',
+            'business_appointment_website/static/src/components/appointment_booking/appointment_booking.xml',
+        ],
     },
     "demo": [],
     "qweb": [],
--- a/business_appointment_website/controllers/main.py
+++ b/business_appointment_website/controllers/main.py
@@ -10,7 +10,7 @@
 from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
 from odoo.addons.website.controllers.main import Website
 from odoo.tools.translate import _
-from odoo.tools import html_escape
+from odoo.tools import escape as html_escape
 
 
 class CustomerPortal(CustomerPortal):
--- a/business_appointment_website/static/src/js/slots.js
+++ /dev/null
deleted file mode 100644
--- /dev/null
+++ b/business_appointment_website/static/src/components/appointment_booking/appointment_booking.js
@@ -0,0 +1,114 @@
+/** @odoo-module **/
+
+import publicWidget from "@web/legacy/js/public/public_widget";
+import { renderToElement } from "@web/core/utils/render";
+
+publicWidget.registry.websiteAppointmentBooking = publicWidget.Widget.extend({
+    selector: '.o_ba_booking_form',
+    events: {
+        'change #service_id': '_onServiceChange',
+        'change #resource_id': '_onResourceChange',
+        'change #booking_date': '_onDateChange',
+    },
+
+    init() {
+        this._super(...arguments);
+        this.rpc = this.bindService("rpc");
+    },
+
+    start: function () {
+        this.$slotsContainer = this.$('.o_ba_time_slots_container');
+        this.$serviceSelect = this.$('#service_id');
+        this.$resourceSelect = this.$('#resource_id');
+        this.$dateSelect = this.$('#booking_date');
+        this.$submitButton = this.$('button[type="submit"]');
+
+        this._updateResources();
+        return this._super.apply(this, arguments);
+    },
+
+    _onServiceChange: function () {
+        this._updateResources();
+        this._fetchSlots();
+    },
+
+    _onResourceChange: function () {
+        this._fetchSlots();
+    },
+
+    _onDateChange: function () {
+        this._fetchSlots();
+    },
+
+    _updateResources: function () {
+        const serviceId = parseInt(this.$serviceSelect.val());
+        const previouslySelected = this.$resourceSelect.val();
+        this.$resourceSelect.empty();
+
+        const allResources = JSON.parse(this.$el.find('#all_resources_data').text());
+        const availableResources = allResources[serviceId] || [];
+
+        availableResources.forEach(res => {
+            this.$resourceSelect.append(`<option value="${res.id}">${res.name}</option>`);
+        });
+
+        if (availableResources.find(res => res.id == previouslySelected)) {
+            this.$resourceSelect.val(previouslySelected);
+        }
+        this.$resourceSelect.prop('disabled', !availableResources.length);
+    },
+
+    _fetchSlots: function () {
+        const serviceId = parseInt(this.$serviceSelect.val());
+        const resourceId = parseInt(this.$resourceSelect.val());
+        const bookingDate = this.$dateSelect.val();
+
+        this.$slotsContainer.empty();
+        this.$submitButton.addClass('disabled');
+
+        if (!serviceId || !resourceId || !bookingDate) {
+            return;
+        }
+
+        this.$slotsContainer.html('<div class="text-center">Loading...</div>');
+
+        this.rpc('/get/appointment/slots', {
+            service_id: serviceId,
+            resource_id: resourceId,
+            date: bookingDate,
+        }).then(slots => {
+            this.$slotsContainer.empty();
+            if (slots.length) {
+                slots.forEach(slot => {
+                    const $slotButton = $(renderToElement('business_appointment_website.booking_slot_button', { slot }));
+                    $slotButton.on('click', this._onSlotClick.bind(this));
+                    this.$slotsContainer.append($slotButton);
+                });
+            } else {
+                this.$slotsContainer.html('<div class="alert alert-warning">No available slots for the selected date.</div>');
+            }
+        });
+    },
+
+    _onSlotClick: function (ev) {
+        const $target = $(ev.currentTarget);
+        if ($target.hasClass('disabled')) {
+            return;
+        }
+        this.$slotsContainer.find('.btn-primary').removeClass('btn-primary').addClass('btn-outline-primary');
+        $target.removeClass('btn-outline-primary').addClass('btn-primary');
+
+        this.$('input[name="slot_id"]').val($target.data('slot-id'));
+        this.$('input[name="start_time"]').val($target.data('start-time'));
+        this.$('input[name="end_time"]').val($target.data('end-time'));
+        this.$submitButton.removeClass('disabled');
+    },
+});
+
+export default publicWidget.registry.websiteAppointmentBooking;
+
--- /dev/null
+++ b/business_appointment_website/static/src/components/appointment_booking/appointment_booking.xml
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<templates xml:space="preserve">
+    <t t-name="business_appointment_website.booking_slot_button" owl="1">
+        <button type="button" class="btn m-1" t-att-class="slot.slot_is_busy ? 'btn-secondary disabled' : 'btn-outline-primary'" t-att-data-slot-id="slot.id" t-att-data-start-time="slot.float_start" t-att-data-end-time="slot.float_end" t-att-disabled="slot.slot_is_busy ? 'disabled' : undefined"><t t-esc="slot.name"/></button>
+    </t>
+</templates>
--- a/business_appointment_website/views/res_config_settings.xml
+++ /dev/null
deleted file mode 100644
--- a/business_appointment_website_sale/__manifest__.py
+++ b/business_appointment_website_sale/__manifest__.py
@@ -1,6 +1,6 @@
 # -*- coding: utf-8 -*-
 {
-    "name": "Universal Appointments: Website Sales",
-    "version": "15.0.1.0.1",
+    "name": "Universal Appointments: Website Sales",    
+    "version": "17.0.1.0.0",
     "category": "Extra Tools",
     "summary": """The extension to manage appointments and reservations from website and portal with payments""",
     "author": "Odoo Tools",
--- a/business_appointment_website_sale/views/res_config_settings.xml
+++ /dev/null
deleted file mode 100644
--- a/business_appointment_hr/__manifest__.py
+++ b/business_appointment_hr/__manifest__.py
@@ -1,6 +1,6 @@
 # -*- coding: utf-8 -*-
 {
-    "name": "Universal Appointments: HR",
-    "version": "15.0.1.0.0",
+    "name": "Universal Appointments: HR",    
+    "version": "17.0.1.0.0",
     "category": "Extra Tools",
     "summary": """The extension to link employees to business resources""",
     "author": "Odoo Tools",
```