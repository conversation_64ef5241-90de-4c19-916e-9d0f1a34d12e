# Translation of CBMS Server.
# This file contains the translation of the following modules:
# 	* business_appointment
#
msgid ""
msgstr ""
"Project-Id-Version: CBMS Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-01 15:35+0000\n"
"PO-Revision-Date: 2022-06-01 15:35+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__start_round_rule
msgid ""
"\n"
"            Define how appointment start time should be rounded. E.g. appointments are available now from 14:12 \n"
"            tomorrow. Then:\n"
"             * 0:05 - rounding for 5 minutes - would round start to 14:15\n"
"             * 0:10 - rounding for 10 minutes - would round start to 14:20\n"
"             * 0:30 - rounding for 30 minutes - would round start to 14:30\n"
"             * 1:00 - rounding for an hour - would round start to 15:00\n"
"             * 02:00 - rounding for 2 hours - would round start to 16:00\n"
"             * 24:00 - rounding for a day - would round start to 00:00\n"
"             * 32:00 - rounding for a day and 8 hours - would round to 08:00 the next day\n"
"            Take into account: ROUNDING IS DONE IN WORKING CALENDAR TIMEZONE\n"
"        "
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/alarm_task.py:0
#, python-format
msgid " Contact: {}"
msgstr "اتصال: {}"

#. module: business_appointment
#: model:sms.template,body:business_appointment.sms_template_default_reminder
msgid ""
" Hello. This is the reminder about the appointment {{ object.name or '' }} "
"scheduled to  {{ object.return_scheduled_time_tz(True) or ''}}"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/alarm_task.py:0
#, python-format
msgid " Resource: {};"
msgstr "المورد: {} "

#. module: business_appointment
#: code:addons/business_appointment/models/alarm_task.py:0
#, python-format
msgid " Service: {};"
msgstr "خدمة: {}؛"

#. module: business_appointment
#: model:mail.template,subject:business_appointment.email_template_successful_appointment
msgid ""
" {{ ctx['target_company'].name }}: Appointment {{ ctx[\"reshedule\"] and "
"'Re-Scheduled' or 'Confirmed' }}"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__extra_working_calendar_id
msgid ""
"\"If defined, this service would be available only in intervals that simultaneously suit this calendar\n"
"        and appointment resource calendar"
msgstr ""

#. module: business_appointment
#: model:mail.template,report_name:business_appointment.email_template_successful_appointment
msgid "#{(object.name or '').replace('/','_')}"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/slots_widget_core.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "(10 Euro extra) is required"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "(48 Euros extra) is required"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "(99 Euro extra) is required"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"(distributed free) is required. \n"
"                                    The app 'HR' would be automatically installed in your database."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__allocation_type
#: model:ir.model.fields,help:business_appointment.field_make_business_appointment__allocation_type
msgid ""
"* If automatic: users would not have to select resources: CBMS would construct time slots for all \n"
"                  resources of chosen type;\n"
"                * If manual: a user would have to choose one or a few resources to observe time slots"
msgstr ""

#. module: business_appointment
#: model:mail.template,body_html:business_appointment.email_template_default_reminder
msgid ""
"<div>\n"
"    Hello,\n"
"    <br/>\n"
"    we are writing to remind about the appointment:                              \n"
"    <br/>\n"
"    <ul>\n"
"        <li>Reference: <t t-out=\"object.name  or ''\"/></li>\n"
"        <li>Scheduled Time: <t t-out=\"object.return_scheduled_time_tz(True) or ''\"/></li>\n"
"        <li>Resource: <t t-out=\"object.resource_id.name or ''\"/></li>\n"
"        <li>Service: <t t-out=\"object.service_id.name or ''\"/></li>\n"
"        <li t-if=\"object.resource_id.location or object.service_id.location\">\n"
"            <t t-out=\"bject.service_id.location or object.resource_id.location or ''\"/>\n"
"        </li>\n"
"    </ul>  \n"
"</div>\n"
"            "
msgstr ""

#. module: business_appointment
#: model:mail.template,body_html:business_appointment.email_template_rating_appointment
msgid ""
"<div>\n"
"<t t-set=\"access_token\" t-value=\"object.rating_get_access_token()\"/>             \n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"<tbody>\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                            <div>\n"
"                                Hello \n"
"                                <t t-if=\"object.partner_id\">\n"
"                                    <t t-out=\"object.partner_id.name\"/>,\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <t t-out=\"object.contact_name\"/>,\n"
"                                </t>\n"
"                                <br/>\n"
"                                Please take a moment to rate our services related to the appointment \"<strong><t t-out=\"object.name\"/></strong>\":                            \n"
"                                <br/>\n"
"                                <ul>\n"
"                                    <li>Resource: <t t-out=\"object.resource_id.name or ''\"/></li>\n"
"                                    <li>Service: <t t-out=\"object.service_id.name or ''\"/></li>\n"
"                                    <li t-if=\"object.resource_id.location or object.service_id.location\">\n"
"                                        <t t-out=\"bject.service_id.location or object.resource_id.location or ''\"/>\n"
"                                    </li>\n"
"                                    <li>Scheduled Time: <t t-out=\"object.return_scheduled_time_tz(True) or ''\"/></li>\n"
"                                </ul>\n"
"                            </div>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <tr>\n"
"                        <td style=\"text-align:center;\">\n"
"                            <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <tr>\n"
"        <td style=\"text-align: center; min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr>\n"
"                    <td style=\"font-size: 13px;\">\n"
"                        <strong>Tell us how you feel about our service</strong><br/>\n"
"                        <span style=\"text-color: #888888\">(click on one of these smileys)</span>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"font-size: 13px;\">\n"
"                        <table style=\"width:100%;text-align:center;\">\n"
"                            <tr>\n"
"                                <td>\n"
"                                    <a t-attf-href=\"/rating/#{access_token}/10\">\n"
"                                        <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                    </a>\n"
"                                </td>\n"
"                                <td>\n"
"                                    <a t-attf-href=\"/rating/#{access_token}/5\">\n"
"                                        <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                    </a>\n"
"                                </td>\n"
"                                <td>\n"
"                                    <a t-attf-href=\"/rating/#{access_token}/1\">\n"
"                                        <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                    </a>\n"
"                                </td>\n"
"                            </tr>\n"
"                        </table>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us to improve continuously.\n"
"        </td>\n"
"    </tr>    \n"
"</tbody>\n"
"</table> \n"
"</div>\n"
"            "
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_kanban
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_kanban
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_kanban
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_kanban
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_kanban
msgid ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"Percentage of satisfaction\"/>"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_form
msgid ""
"<span class=\"fa fa-edit ba_icon\"> </span>\n"
"                                <span>Re-Schedule</span>"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_form
msgid "<span class=\"o_stat_text\">%</span>"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.report_business_appointment_single
msgid "<strong> Location:</strong>"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.report_business_appointment_single
msgid "<strong>Resource:</strong>"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.report_business_appointment_single
msgid "<strong>Scheduled Time:</strong>"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.report_business_appointment_single
msgid "<strong>Service:</strong>"
msgstr ""

#. module: business_appointment
#: model:mail.template,body_html:business_appointment.email_template_successful_appointment
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tbody><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td valign=\"middle\" align=\"right\">\n"
"                            <img t-attf-src=\"#{ctx['base_url']}/logo.png?company=#{ctx['target_company'].id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-attf-alt=\"#{ctx['target_company'].name}\"/>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <tr>\n"
"                        <td colspan=\"2\" style=\"text-align:center;\">\n"
"                            <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                            <div>\n"
"                                Hello \n"
"                                <t t-if=\"object.partner_id\">\n"
"                                    <t t-out=\"object.partner_id.name\"/>,\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <t t-out=\"object.contact_name\"/>,\n"
"                                </t>\n"
"                                <br/>\n"
"                                thank you! \n"
"\n"
"                                <t t-if=\"ctx['reshedule']\">\n"
"                                     Your appointment is re-scheduled:\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    Your appointment is scheduled:\n"
"                                </t>                           \n"
"                                <br/>\n"
"                                <ul>\n"
"                                    <li>Reference: <t t-out=\"object.name  or ''\"/></li>\n"
"                                    <li>Scheduled Time: <t t-out=\"object.return_scheduled_time_tz(True) or ''\"/></li>\n"
"                                    <li>Resource: <t t-out=\"object.resource_id.name or ''\"/></li>\n"
"                                    <li>Service: <t t-out=\"object.service_id.name or ''\"/></li>\n"
"                                    <li t-if=\"object.resource_id.location or object.service_id.location\">\n"
"                                        <t t-out=\"bject.service_id.location or object.resource_id.location or ''\"/>\n"
"                                    </li>\n"
"                                </ul>\n"
"                            </div>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <tr>\n"
"                        <td style=\"text-align:center;\">\n"
"                            <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td valign=\"middle\" align=\"left\">\n"
"                            <t t-out=\"ctx['target_company'].name\"/>\n"
"                        </td>\n"
"                    </tr>\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                        <t t-out=\"ctx['target_company'].phone\"/>\n"
"                        <t t-if=\"ctx['target_company'].email\">\n"
"                            | \n"
"                            <a t-attf-href=\"'mailto:%s' % #{ctx['target_company'].email}\" style=\"color: #454748;\">\n"
"                                <t t-out=\"ctx['target_company'].email\"/>\n"
"                            </a>\n"
"                        </t>\n"
"                        | \n"
"                        <a t-attf-href=\"#{ctx['website_http_domain']}\" style=\" color: #454748;\">\n"
"                            <t t-out=\"ctx['website_http_domain']\"/>\n"
"                        </a>\n"
"                    </td>\n"
"                </tr>\n"
"                </tbody>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr></tbody></table>   \n"
"            "
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_needaction
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_needaction
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_needaction
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_needaction
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_needaction
msgid "Action Needed"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__active
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__active
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__active
msgid "Active"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__activity_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__activity_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__activity_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__activity_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__activity_ids
msgid "Activities"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "Activities Todo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__activity_exception_decoration
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__activity_exception_decoration
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__activity_exception_decoration
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__activity_exception_decoration
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__activity_state
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__activity_state
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__activity_state
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__activity_state
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__activity_state
msgid "Activity State"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__activity_type_icon
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__activity_type_icon
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__activity_type_icon
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__activity_type_icon
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/slots_widget_core.js:0
#, python-format
msgid "Add"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/time_slots.xml:0
#: code:addons/business_appointment/static/src/xml/time_slots.xml:0
#, python-format
msgid "Add one"
msgstr "أضف واحدا"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.choose_appointment_customert_form_view
msgid "Address"
msgstr "عنوان"

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/abidjan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/abidjan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/abidjan
msgid "Africa/Abidjan"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/accra
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/accra
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/accra
msgid "Africa/Accra"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/addis_ababa
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/addis_ababa
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/addis_ababa
msgid "Africa/Addis_Ababa"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/algiers
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/algiers
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/algiers
msgid "Africa/Algiers"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/asmara
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/asmara
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/asmara
msgid "Africa/Asmara"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/asmera
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/asmera
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/asmera
msgid "Africa/Asmera"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/bamako
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/bamako
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/bamako
msgid "Africa/Bamako"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/bangui
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/bangui
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/bangui
msgid "Africa/Bangui"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/banjul
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/banjul
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/banjul
msgid "Africa/Banjul"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/bissau
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/bissau
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/bissau
msgid "Africa/Bissau"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/blantyre
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/blantyre
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/blantyre
msgid "Africa/Blantyre"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/brazzaville
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/brazzaville
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/brazzaville
msgid "Africa/Brazzaville"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/bujumbura
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/bujumbura
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/bujumbura
msgid "Africa/Bujumbura"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/cairo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/cairo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/cairo
msgid "Africa/Cairo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/casablanca
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/casablanca
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/casablanca
msgid "Africa/Casablanca"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/ceuta
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/ceuta
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/ceuta
msgid "Africa/Ceuta"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/conakry
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/conakry
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/conakry
msgid "Africa/Conakry"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/dakar
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/dakar
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/dakar
msgid "Africa/Dakar"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/dar_es_salaam
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/dar_es_salaam
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/dar_es_salaam
msgid "Africa/Dar_es_Salaam"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/djibouti
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/djibouti
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/djibouti
msgid "Africa/Djibouti"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/douala
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/douala
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/douala
msgid "Africa/Douala"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/el_aaiun
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/el_aaiun
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/el_aaiun
msgid "Africa/El_Aaiun"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/freetown
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/freetown
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/freetown
msgid "Africa/Freetown"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/gaborone
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/gaborone
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/gaborone
msgid "Africa/Gaborone"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/harare
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/harare
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/harare
msgid "Africa/Harare"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/johannesburg
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/johannesburg
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/johannesburg
msgid "Africa/Johannesburg"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/juba
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/juba
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/juba
msgid "Africa/Juba"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/kampala
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/kampala
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/kampala
msgid "Africa/Kampala"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/khartoum
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/khartoum
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/khartoum
msgid "Africa/Khartoum"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/kigali
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/kigali
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/kigali
msgid "Africa/Kigali"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/kinshasa
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/kinshasa
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/kinshasa
msgid "Africa/Kinshasa"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/lagos
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/lagos
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/lagos
msgid "Africa/Lagos"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/libreville
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/libreville
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/libreville
msgid "Africa/Libreville"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/lome
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/lome
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/lome
msgid "Africa/Lome"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/luanda
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/luanda
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/luanda
msgid "Africa/Luanda"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/lubumbashi
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/lubumbashi
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/lubumbashi
msgid "Africa/Lubumbashi"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/lusaka
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/lusaka
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/lusaka
msgid "Africa/Lusaka"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/malabo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/malabo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/malabo
msgid "Africa/Malabo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/maputo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/maputo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/maputo
msgid "Africa/Maputo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/maseru
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/maseru
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/maseru
msgid "Africa/Maseru"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/mbabane
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/mbabane
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/mbabane
msgid "Africa/Mbabane"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/mogadishu
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/mogadishu
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/mogadishu
msgid "Africa/Mogadishu"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/monrovia
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/monrovia
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/monrovia
msgid "Africa/Monrovia"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/nairobi
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/nairobi
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/nairobi
msgid "Africa/Nairobi"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/ndjamena
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/ndjamena
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/ndjamena
msgid "Africa/Ndjamena"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/niamey
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/niamey
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/niamey
msgid "Africa/Niamey"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/nouakchott
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/nouakchott
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/nouakchott
msgid "Africa/Nouakchott"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/ouagadougou
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/ouagadougou
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/ouagadougou
msgid "Africa/Ouagadougou"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/porto-novo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/porto-novo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/porto-novo
msgid "Africa/Porto-Novo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/sao_tome
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/sao_tome
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/sao_tome
msgid "Africa/Sao_Tome"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/timbuktu
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/timbuktu
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/timbuktu
msgid "Africa/Timbuktu"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/tripoli
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/tripoli
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/tripoli
msgid "Africa/Tripoli"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/tunis
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/tunis
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/tunis
msgid "Africa/Tunis"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__africa/windhoek
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__africa/windhoek
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__africa/windhoek
msgid "Africa/Windhoek"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_res_config_settings__ba_confirmation_retry_trials
msgid "After exceeding the button 'Resend Code' would not be shown"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_res_config_settings__ba_max_approval_trials
msgid ""
"After exceeding this number all steps would be cancelled and should be "
"started from scratch"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_res_config_settings__ba_max_approval_time
msgid "After this period, not confirmed appointments would be cancelled"
msgstr "بعد هذه الفترة ، سيتم إلغاء المواعيد غير المؤكدة"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__agree_terms
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__agree_terms
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__agree_terms
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__agree_terms
msgid "Agree on terms and conditions"
msgstr "وافق على الشروط والأحكام"

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_res_config_settings__ba_confirmation_retry_period
msgid "Agter this period it would be possible to resend the confirmation code"
msgstr "بعد هذه الفترة سيكون من الممكن إعادة إرسال رمز التأكيد"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_alarm_task__alarm_id
#: model_terms:ir.ui.view,arch_db:business_appointment.alarm_task_view_search
msgid "Alarm"
msgstr "إنذار"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_alarm_task__alarm_time
msgid "Alarm Time"
msgstr "وقت التنبيه"

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.appointment_alarm_action
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__alarm_ids
msgid "Alarms"
msgstr "إنذارات"

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_alarm__recipients__portal
msgid "All external followers (portal)"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_alarm__recipients__everybody
msgid "All followers"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_alarm__recipients__internal
msgid "All internal followers"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__allocation_factor
msgid "Allocation Factor"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
msgid "Allocation Type"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__manual_duration
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__manual_duration
msgid "Allow Manual Duration"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__manual_duration
#: model:ir.model.fields,help:business_appointment.field_make_business_appointment__manual_duration
msgid ""
"Allow users to define appointment duration. Otherwise, it would be always "
"default"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_resource_type.py:0
#: model:ir.model.constraint,message:business_appointment.constraint_business_resource_type_allowed_to_check
#, python-format
msgid "Allowed after period should be positive!"
msgstr ""

#. module: business_appointment
#: model:ir.model.constraint,message:business_appointment.constraint_business_resource_type_allowed_from_check
msgid "Allowed before period should be positive!"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/adak
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/adak
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/adak
msgid "America/Adak"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/anchorage
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/anchorage
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/anchorage
msgid "America/Anchorage"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/anguilla
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/anguilla
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/anguilla
msgid "America/Anguilla"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/antigua
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/antigua
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/antigua
msgid "America/Antigua"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/araguaina
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/araguaina
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/araguaina
msgid "America/Araguaina"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/buenos_aires
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/buenos_aires
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/buenos_aires
msgid "America/Argentina/Buenos_Aires"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/catamarca
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/catamarca
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/catamarca
msgid "America/Argentina/Catamarca"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/comodrivadavia
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/comodrivadavia
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/comodrivadavia
msgid "America/Argentina/ComodRivadavia"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/cordoba
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/cordoba
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/cordoba
msgid "America/Argentina/Cordoba"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/jujuy
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/jujuy
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/jujuy
msgid "America/Argentina/Jujuy"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/la_rioja
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/la_rioja
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/la_rioja
msgid "America/Argentina/La_Rioja"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/mendoza
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/mendoza
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/mendoza
msgid "America/Argentina/Mendoza"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/rio_gallegos
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/rio_gallegos
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/rio_gallegos
msgid "America/Argentina/Rio_Gallegos"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/salta
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/salta
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/salta
msgid "America/Argentina/Salta"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/san_juan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/san_juan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/san_juan
msgid "America/Argentina/San_Juan"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/san_luis
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/san_luis
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/san_luis
msgid "America/Argentina/San_Luis"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/tucuman
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/tucuman
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/tucuman
msgid "America/Argentina/Tucuman"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/argentina/ushuaia
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/argentina/ushuaia
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/argentina/ushuaia
msgid "America/Argentina/Ushuaia"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/aruba
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/aruba
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/aruba
msgid "America/Aruba"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/asuncion
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/asuncion
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/asuncion
msgid "America/Asuncion"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/atikokan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/atikokan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/atikokan
msgid "America/Atikokan"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/atka
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/atka
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/atka
msgid "America/Atka"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/bahia
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/bahia
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/bahia
msgid "America/Bahia"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/bahia_banderas
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/bahia_banderas
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/bahia_banderas
msgid "America/Bahia_Banderas"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/barbados
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/barbados
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/barbados
msgid "America/Barbados"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/belem
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/belem
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/belem
msgid "America/Belem"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/belize
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/belize
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/belize
msgid "America/Belize"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/blanc-sablon
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/blanc-sablon
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/blanc-sablon
msgid "America/Blanc-Sablon"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/boa_vista
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/boa_vista
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/boa_vista
msgid "America/Boa_Vista"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/bogota
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/bogota
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/bogota
msgid "America/Bogota"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/boise
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/boise
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/boise
msgid "America/Boise"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/buenos_aires
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/buenos_aires
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/buenos_aires
msgid "America/Buenos_Aires"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/cambridge_bay
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/cambridge_bay
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/cambridge_bay
msgid "America/Cambridge_Bay"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/campo_grande
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/campo_grande
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/campo_grande
msgid "America/Campo_Grande"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/cancun
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/cancun
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/cancun
msgid "America/Cancun"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/caracas
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/caracas
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/caracas
msgid "America/Caracas"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/catamarca
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/catamarca
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/catamarca
msgid "America/Catamarca"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/cayenne
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/cayenne
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/cayenne
msgid "America/Cayenne"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/cayman
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/cayman
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/cayman
msgid "America/Cayman"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/chicago
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/chicago
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/chicago
msgid "America/Chicago"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/chihuahua
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/chihuahua
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/chihuahua
msgid "America/Chihuahua"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/coral_harbour
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/coral_harbour
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/coral_harbour
msgid "America/Coral_Harbour"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/cordoba
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/cordoba
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/cordoba
msgid "America/Cordoba"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/costa_rica
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/costa_rica
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/costa_rica
msgid "America/Costa_Rica"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/creston
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/creston
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/creston
msgid "America/Creston"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/cuiaba
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/cuiaba
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/cuiaba
msgid "America/Cuiaba"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/curacao
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/curacao
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/curacao
msgid "America/Curacao"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/danmarkshavn
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/danmarkshavn
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/danmarkshavn
msgid "America/Danmarkshavn"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/dawson
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/dawson
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/dawson
msgid "America/Dawson"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/dawson_creek
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/dawson_creek
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/dawson_creek
msgid "America/Dawson_Creek"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/denver
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/denver
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/denver
msgid "America/Denver"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/detroit
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/detroit
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/detroit
msgid "America/Detroit"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/dominica
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/dominica
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/dominica
msgid "America/Dominica"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/edmonton
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/edmonton
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/edmonton
msgid "America/Edmonton"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/eirunepe
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/eirunepe
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/eirunepe
msgid "America/Eirunepe"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/el_salvador
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/el_salvador
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/el_salvador
msgid "America/El_Salvador"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/ensenada
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/ensenada
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/ensenada
msgid "America/Ensenada"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/fort_nelson
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/fort_nelson
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/fort_nelson
msgid "America/Fort_Nelson"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/fort_wayne
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/fort_wayne
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/fort_wayne
msgid "America/Fort_Wayne"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/fortaleza
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/fortaleza
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/fortaleza
msgid "America/Fortaleza"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/glace_bay
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/glace_bay
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/glace_bay
msgid "America/Glace_Bay"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/godthab
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/godthab
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/godthab
msgid "America/Godthab"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/goose_bay
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/goose_bay
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/goose_bay
msgid "America/Goose_Bay"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/grand_turk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/grand_turk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/grand_turk
msgid "America/Grand_Turk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/grenada
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/grenada
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/grenada
msgid "America/Grenada"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/guadeloupe
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/guadeloupe
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/guadeloupe
msgid "America/Guadeloupe"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/guatemala
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/guatemala
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/guatemala
msgid "America/Guatemala"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/guayaquil
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/guayaquil
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/guayaquil
msgid "America/Guayaquil"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/guyana
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/guyana
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/guyana
msgid "America/Guyana"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/halifax
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/halifax
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/halifax
msgid "America/Halifax"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/havana
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/havana
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/havana
msgid "America/Havana"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/hermosillo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/hermosillo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/hermosillo
msgid "America/Hermosillo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/indiana/indianapolis
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/indiana/indianapolis
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/indiana/indianapolis
msgid "America/Indiana/Indianapolis"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/indiana/knox
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/indiana/knox
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/indiana/knox
msgid "America/Indiana/Knox"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/indiana/marengo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/indiana/marengo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/indiana/marengo
msgid "America/Indiana/Marengo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/indiana/petersburg
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/indiana/petersburg
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/indiana/petersburg
msgid "America/Indiana/Petersburg"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/indiana/tell_city
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/indiana/tell_city
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/indiana/tell_city
msgid "America/Indiana/Tell_City"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/indiana/vevay
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/indiana/vevay
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/indiana/vevay
msgid "America/Indiana/Vevay"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/indiana/vincennes
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/indiana/vincennes
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/indiana/vincennes
msgid "America/Indiana/Vincennes"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/indiana/winamac
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/indiana/winamac
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/indiana/winamac
msgid "America/Indiana/Winamac"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/indianapolis
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/indianapolis
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/indianapolis
msgid "America/Indianapolis"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/inuvik
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/inuvik
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/inuvik
msgid "America/Inuvik"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/iqaluit
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/iqaluit
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/iqaluit
msgid "America/Iqaluit"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/jamaica
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/jamaica
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/jamaica
msgid "America/Jamaica"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/jujuy
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/jujuy
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/jujuy
msgid "America/Jujuy"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/juneau
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/juneau
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/juneau
msgid "America/Juneau"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/kentucky/louisville
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/kentucky/louisville
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/kentucky/louisville
msgid "America/Kentucky/Louisville"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/kentucky/monticello
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/kentucky/monticello
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/kentucky/monticello
msgid "America/Kentucky/Monticello"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/knox_in
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/knox_in
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/knox_in
msgid "America/Knox_IN"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/kralendijk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/kralendijk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/kralendijk
msgid "America/Kralendijk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/la_paz
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/la_paz
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/la_paz
msgid "America/La_Paz"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/lima
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/lima
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/lima
msgid "America/Lima"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/los_angeles
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/los_angeles
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/los_angeles
msgid "America/Los_Angeles"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/louisville
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/louisville
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/louisville
msgid "America/Louisville"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/lower_princes
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/lower_princes
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/lower_princes
msgid "America/Lower_Princes"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/maceio
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/maceio
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/maceio
msgid "America/Maceio"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/managua
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/managua
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/managua
msgid "America/Managua"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/manaus
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/manaus
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/manaus
msgid "America/Manaus"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/marigot
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/marigot
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/marigot
msgid "America/Marigot"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/martinique
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/martinique
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/martinique
msgid "America/Martinique"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/matamoros
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/matamoros
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/matamoros
msgid "America/Matamoros"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/mazatlan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/mazatlan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/mazatlan
msgid "America/Mazatlan"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/mendoza
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/mendoza
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/mendoza
msgid "America/Mendoza"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/menominee
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/menominee
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/menominee
msgid "America/Menominee"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/merida
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/merida
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/merida
msgid "America/Merida"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/metlakatla
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/metlakatla
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/metlakatla
msgid "America/Metlakatla"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/mexico_city
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/mexico_city
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/mexico_city
msgid "America/Mexico_City"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/miquelon
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/miquelon
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/miquelon
msgid "America/Miquelon"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/moncton
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/moncton
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/moncton
msgid "America/Moncton"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/monterrey
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/monterrey
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/monterrey
msgid "America/Monterrey"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/montevideo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/montevideo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/montevideo
msgid "America/Montevideo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/montreal
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/montreal
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/montreal
msgid "America/Montreal"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/montserrat
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/montserrat
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/montserrat
msgid "America/Montserrat"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/nassau
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/nassau
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/nassau
msgid "America/Nassau"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/new_york
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/new_york
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/new_york
msgid "America/New_York"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/nipigon
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/nipigon
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/nipigon
msgid "America/Nipigon"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/nome
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/nome
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/nome
msgid "America/Nome"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/noronha
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/noronha
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/noronha
msgid "America/Noronha"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/north_dakota/beulah
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/north_dakota/beulah
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/north_dakota/beulah
msgid "America/North_Dakota/Beulah"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/north_dakota/center
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/north_dakota/center
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/north_dakota/center
msgid "America/North_Dakota/Center"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/north_dakota/new_salem
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/north_dakota/new_salem
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/north_dakota/new_salem
msgid "America/North_Dakota/New_Salem"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/ojinaga
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/ojinaga
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/ojinaga
msgid "America/Ojinaga"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/panama
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/panama
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/panama
msgid "America/Panama"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/pangnirtung
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/pangnirtung
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/pangnirtung
msgid "America/Pangnirtung"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/paramaribo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/paramaribo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/paramaribo
msgid "America/Paramaribo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/phoenix
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/phoenix
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/phoenix
msgid "America/Phoenix"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/port-au-prince
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/port-au-prince
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/port-au-prince
msgid "America/Port-au-Prince"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/port_of_spain
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/port_of_spain
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/port_of_spain
msgid "America/Port_of_Spain"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/porto_acre
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/porto_acre
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/porto_acre
msgid "America/Porto_Acre"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/porto_velho
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/porto_velho
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/porto_velho
msgid "America/Porto_Velho"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/puerto_rico
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/puerto_rico
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/puerto_rico
msgid "America/Puerto_Rico"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/punta_arenas
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/punta_arenas
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/punta_arenas
msgid "America/Punta_Arenas"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/rainy_river
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/rainy_river
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/rainy_river
msgid "America/Rainy_River"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/rankin_inlet
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/rankin_inlet
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/rankin_inlet
msgid "America/Rankin_Inlet"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/recife
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/recife
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/recife
msgid "America/Recife"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/regina
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/regina
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/regina
msgid "America/Regina"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/resolute
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/resolute
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/resolute
msgid "America/Resolute"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/rio_branco
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/rio_branco
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/rio_branco
msgid "America/Rio_Branco"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/rosario
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/rosario
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/rosario
msgid "America/Rosario"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/santa_isabel
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/santa_isabel
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/santa_isabel
msgid "America/Santa_Isabel"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/santarem
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/santarem
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/santarem
msgid "America/Santarem"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/santiago
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/santiago
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/santiago
msgid "America/Santiago"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/santo_domingo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/santo_domingo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/santo_domingo
msgid "America/Santo_Domingo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/sao_paulo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/sao_paulo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/sao_paulo
msgid "America/Sao_Paulo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/scoresbysund
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/scoresbysund
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/scoresbysund
msgid "America/Scoresbysund"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/shiprock
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/shiprock
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/shiprock
msgid "America/Shiprock"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/sitka
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/sitka
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/sitka
msgid "America/Sitka"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/st_barthelemy
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/st_barthelemy
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/st_barthelemy
msgid "America/St_Barthelemy"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/st_johns
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/st_johns
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/st_johns
msgid "America/St_Johns"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/st_kitts
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/st_kitts
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/st_kitts
msgid "America/St_Kitts"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/st_lucia
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/st_lucia
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/st_lucia
msgid "America/St_Lucia"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/st_thomas
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/st_thomas
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/st_thomas
msgid "America/St_Thomas"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/st_vincent
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/st_vincent
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/st_vincent
msgid "America/St_Vincent"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/swift_current
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/swift_current
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/swift_current
msgid "America/Swift_Current"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/tegucigalpa
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/tegucigalpa
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/tegucigalpa
msgid "America/Tegucigalpa"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/thule
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/thule
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/thule
msgid "America/Thule"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/thunder_bay
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/thunder_bay
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/thunder_bay
msgid "America/Thunder_Bay"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/tijuana
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/tijuana
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/tijuana
msgid "America/Tijuana"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/toronto
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/toronto
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/toronto
msgid "America/Toronto"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/tortola
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/tortola
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/tortola
msgid "America/Tortola"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/vancouver
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/vancouver
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/vancouver
msgid "America/Vancouver"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/virgin
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/virgin
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/virgin
msgid "America/Virgin"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/whitehorse
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/whitehorse
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/whitehorse
msgid "America/Whitehorse"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/winnipeg
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/winnipeg
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/winnipeg
msgid "America/Winnipeg"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/yakutat
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/yakutat
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/yakutat
msgid "America/Yakutat"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__america/yellowknife
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__america/yellowknife
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__america/yellowknife
msgid "America/Yellowknife"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/casey
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/casey
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/casey
msgid "Antarctica/Casey"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/davis
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/davis
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/davis
msgid "Antarctica/Davis"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/dumontdurville
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/dumontdurville
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/dumontdurville
msgid "Antarctica/DumontDUrville"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/macquarie
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/macquarie
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/macquarie
msgid "Antarctica/Macquarie"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/mawson
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/mawson
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/mawson
msgid "Antarctica/Mawson"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/mcmurdo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/mcmurdo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/mcmurdo
msgid "Antarctica/McMurdo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/palmer
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/palmer
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/palmer
msgid "Antarctica/Palmer"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/rothera
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/rothera
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/rothera
msgid "Antarctica/Rothera"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/south_pole
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/south_pole
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/south_pole
msgid "Antarctica/South_Pole"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/syowa
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/syowa
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/syowa
msgid "Antarctica/Syowa"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/troll
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/troll
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/troll
msgid "Antarctica/Troll"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__antarctica/vostok
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__antarctica/vostok
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__antarctica/vostok
msgid "Antarctica/Vostok"
msgstr ""

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.business_appointment_action_only_form
#: model:ir.model,name:business_appointment.model_business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_alarm_task__appointment_id
#: model:ir.model.fields,field_description:business_appointment.field_associated_product_line__appointment_id
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__appointment_id
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__appointment_id
#: model_terms:ir.ui.view,arch_db:business_appointment.alarm_task_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.rating_rating_view_search_resource_type
msgid "Appointment"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_appointment_alarm
msgid "Appointment Alarm"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__ir_ui_view__type__appointment_calendar
msgid "Appointment Calendar"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__appointment_duration
msgid "Appointment Default Duration"
msgstr "مدة الحجز الافتراضية"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__ba_description
msgid "Appointment Description"
msgstr "وصف الموعد"

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_appointment_group
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__reservation_group_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__reservation_group_id
msgid "Appointment Group"
msgstr ""

#. module: business_appointment
#: model:mail.message.subtype,name:business_appointment.mt_appointment_rating
#: model:mail.message.subtype,name:business_appointment.mt_business_resource_rating
#: model:mail.message.subtype,name:business_appointment.mt_business_resource_type_rating
msgid "Appointment Rating"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__name
msgid "Appointment Reference"
msgstr ""

#. module: business_appointment
#: model:mail.template,subject:business_appointment.email_template_rating_appointment
msgid "Appointment Satisfaction Survey"
msgstr ""

#. module: business_appointment
#: model:mail.message.subtype,description:business_appointment.mt_business_appointment_reserved_time_change
#: model:mail.message.subtype,description:business_appointment.mt_business_resource_reserved_time_change
#: model:mail.message.subtype,description:business_appointment.mt_business_resource_type_reserved_time_change
msgid "Appointment Update"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment_core.py:0
#, python-format
msgid "Appointment for {} by {}"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/business_appointment_calendarview.js:0
#: model:ir.actions.act_window,name:business_appointment.business_appointment_action
#: model:ir.actions.act_window,name:business_appointment.business_appointment_action_from_partner
#: model:ir.model.fields,field_description:business_appointment.field_appointment_group__appointment_ids
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__appointment_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__appointment_ids
#: model:ir.model.fields,field_description:business_appointment.field_res_partner__appointment_ids
#: model:ir.model.fields,field_description:business_appointment.field_res_users__appointment_ids
#: model:ir.ui.menu,name:business_appointment.menu_business_appointments
#: model:ir.ui.menu,name:business_appointment.menu_business_appointments_main
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_kanban
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_kanban
#: model_terms:ir.ui.view,arch_db:business_appointment.res_partner_view_form
#, python-format
msgid "Appointments"
msgstr "المواعيد"

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.appointment_analytic_action
#: model:ir.model,name:business_appointment.model_appointment_analytic
#: model:ir.ui.menu,name:business_appointment.menu_business_analytic
msgid "Appointments Analysis"
msgstr "تحليل المواعيد"

#. module: business_appointment
#: model:mail.message.subtype,name:business_appointment.mt_business_appointment_cancel
#: model:mail.message.subtype,name:business_appointment.mt_business_resource_cancel
#: model:mail.message.subtype,name:business_appointment.mt_business_resource_type_cancel
msgid "Appointments Cancellation"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_partner__appointments_len
#: model:ir.model.fields,field_description:business_appointment.field_res_users__appointments_len
msgid "Appointments Count"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__rating_option
msgid "Appointments Customer Rating"
msgstr ""

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.rating_rating_action_business_appointment
msgid "Appointments Ratings"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__module_business_appointment_website
msgid "Appointments in Portal and Website"
msgstr ""

#. module: business_appointment
#: model:res.groups,name:business_appointment.group_ba_admin
msgid "Appointments: Administrator"
msgstr ""

#. module: business_appointment
#: model:mail.template,name:business_appointment.email_template_default_reminder
msgid "Appointments: Default Email Reminder"
msgstr ""

#. module: business_appointment
#: model:sms.template,name:business_appointment.sms_template_default_reminder
msgid "Appointments: Default SMS Reminder"
msgstr ""

#. module: business_appointment
#: model:res.groups,name:business_appointment.group_ba_user
msgid "Appointments: Only Own"
msgstr ""

#. module: business_appointment
#: model:mail.template,name:business_appointment.email_template_rating_appointment
msgid "Appointments: Rating Template"
msgstr ""

#. module: business_appointment
#: model:mail.template,name:business_appointment.email_template_successful_appointment
msgid "Appointments: Success Email Template"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "Archived"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__arctic/longyearbyen
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__arctic/longyearbyen
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__arctic/longyearbyen
msgid "Arctic/Longyearbyen"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/aden
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/aden
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/aden
msgid "Asia/Aden"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/almaty
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/almaty
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/almaty
msgid "Asia/Almaty"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/amman
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/amman
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/amman
msgid "Asia/Amman"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/anadyr
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/anadyr
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/anadyr
msgid "Asia/Anadyr"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/aqtau
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/aqtau
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/aqtau
msgid "Asia/Aqtau"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/aqtobe
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/aqtobe
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/aqtobe
msgid "Asia/Aqtobe"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/ashgabat
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/ashgabat
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/ashgabat
msgid "Asia/Ashgabat"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/ashkhabad
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/ashkhabad
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/ashkhabad
msgid "Asia/Ashkhabad"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/atyrau
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/atyrau
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/atyrau
msgid "Asia/Atyrau"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/baghdad
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/baghdad
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/baghdad
msgid "Asia/Baghdad"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/bahrain
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/bahrain
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/bahrain
msgid "Asia/Bahrain"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/baku
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/baku
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/baku
msgid "Asia/Baku"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/bangkok
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/bangkok
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/bangkok
msgid "Asia/Bangkok"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/barnaul
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/barnaul
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/barnaul
msgid "Asia/Barnaul"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/beirut
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/beirut
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/beirut
msgid "Asia/Beirut"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/bishkek
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/bishkek
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/bishkek
msgid "Asia/Bishkek"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/brunei
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/brunei
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/brunei
msgid "Asia/Brunei"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/calcutta
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/calcutta
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/calcutta
msgid "Asia/Calcutta"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/chita
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/chita
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/chita
msgid "Asia/Chita"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/choibalsan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/choibalsan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/choibalsan
msgid "Asia/Choibalsan"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/chongqing
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/chongqing
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/chongqing
msgid "Asia/Chongqing"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/chungking
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/chungking
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/chungking
msgid "Asia/Chungking"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/colombo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/colombo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/colombo
msgid "Asia/Colombo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/dacca
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/dacca
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/dacca
msgid "Asia/Dacca"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/damascus
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/damascus
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/damascus
msgid "Asia/Damascus"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/dhaka
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/dhaka
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/dhaka
msgid "Asia/Dhaka"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/dili
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/dili
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/dili
msgid "Asia/Dili"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/dubai
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/dubai
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/dubai
msgid "Asia/Dubai"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/dushanbe
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/dushanbe
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/dushanbe
msgid "Asia/Dushanbe"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/famagusta
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/famagusta
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/famagusta
msgid "Asia/Famagusta"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/gaza
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/gaza
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/gaza
msgid "Asia/Gaza"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/harbin
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/harbin
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/harbin
msgid "Asia/Harbin"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/hebron
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/hebron
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/hebron
msgid "Asia/Hebron"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/ho_chi_minh
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/ho_chi_minh
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/ho_chi_minh
msgid "Asia/Ho_Chi_Minh"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/hong_kong
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/hong_kong
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/hong_kong
msgid "Asia/Hong_Kong"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/hovd
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/hovd
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/hovd
msgid "Asia/Hovd"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/irkutsk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/irkutsk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/irkutsk
msgid "Asia/Irkutsk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/istanbul
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/istanbul
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/istanbul
msgid "Asia/Istanbul"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/jakarta
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/jakarta
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/jakarta
msgid "Asia/Jakarta"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/jayapura
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/jayapura
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/jayapura
msgid "Asia/Jayapura"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/jerusalem
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/jerusalem
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/jerusalem
msgid "Asia/Jerusalem"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/kabul
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/kabul
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/kabul
msgid "Asia/Kabul"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/kamchatka
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/kamchatka
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/kamchatka
msgid "Asia/Kamchatka"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/karachi
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/karachi
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/karachi
msgid "Asia/Karachi"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/kashgar
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/kashgar
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/kashgar
msgid "Asia/Kashgar"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/kathmandu
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/kathmandu
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/kathmandu
msgid "Asia/Kathmandu"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/katmandu
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/katmandu
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/katmandu
msgid "Asia/Katmandu"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/khandyga
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/khandyga
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/khandyga
msgid "Asia/Khandyga"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/kolkata
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/kolkata
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/kolkata
msgid "Asia/Kolkata"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/krasnoyarsk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/krasnoyarsk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/krasnoyarsk
msgid "Asia/Krasnoyarsk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/kuala_lumpur
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/kuala_lumpur
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/kuala_lumpur
msgid "Asia/Kuala_Lumpur"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/kuching
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/kuching
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/kuching
msgid "Asia/Kuching"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/kuwait
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/kuwait
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/kuwait
msgid "Asia/Kuwait"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/macao
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/macao
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/macao
msgid "Asia/Macao"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/macau
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/macau
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/macau
msgid "Asia/Macau"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/magadan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/magadan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/magadan
msgid "Asia/Magadan"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/makassar
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/makassar
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/makassar
msgid "Asia/Makassar"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/manila
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/manila
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/manila
msgid "Asia/Manila"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/muscat
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/muscat
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/muscat
msgid "Asia/Muscat"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/nicosia
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/nicosia
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/nicosia
msgid "Asia/Nicosia"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/novokuznetsk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/novokuznetsk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/novokuznetsk
msgid "Asia/Novokuznetsk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/novosibirsk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/novosibirsk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/novosibirsk
msgid "Asia/Novosibirsk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/omsk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/omsk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/omsk
msgid "Asia/Omsk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/oral
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/oral
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/oral
msgid "Asia/Oral"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/phnom_penh
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/phnom_penh
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/phnom_penh
msgid "Asia/Phnom_Penh"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/pontianak
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/pontianak
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/pontianak
msgid "Asia/Pontianak"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/pyongyang
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/pyongyang
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/pyongyang
msgid "Asia/Pyongyang"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/qatar
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/qatar
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/qatar
msgid "Asia/Qatar"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/qostanay
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/qostanay
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/qostanay
msgid "Asia/Qostanay"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/qyzylorda
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/qyzylorda
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/qyzylorda
msgid "Asia/Qyzylorda"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/rangoon
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/rangoon
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/rangoon
msgid "Asia/Rangoon"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/riyadh
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/riyadh
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/riyadh
msgid "Asia/Riyadh"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/saigon
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/saigon
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/saigon
msgid "Asia/Saigon"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/sakhalin
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/sakhalin
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/sakhalin
msgid "Asia/Sakhalin"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/samarkand
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/samarkand
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/samarkand
msgid "Asia/Samarkand"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/seoul
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/seoul
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/seoul
msgid "Asia/Seoul"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/shanghai
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/shanghai
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/shanghai
msgid "Asia/Shanghai"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/singapore
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/singapore
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/singapore
msgid "Asia/Singapore"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/srednekolymsk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/srednekolymsk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/srednekolymsk
msgid "Asia/Srednekolymsk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/taipei
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/taipei
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/taipei
msgid "Asia/Taipei"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/tashkent
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/tashkent
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/tashkent
msgid "Asia/Tashkent"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/tbilisi
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/tbilisi
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/tbilisi
msgid "Asia/Tbilisi"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/tehran
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/tehran
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/tehran
msgid "Asia/Tehran"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/tel_aviv
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/tel_aviv
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/tel_aviv
msgid "Asia/Tel_Aviv"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/thimbu
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/thimbu
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/thimbu
msgid "Asia/Thimbu"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/thimphu
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/thimphu
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/thimphu
msgid "Asia/Thimphu"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/tokyo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/tokyo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/tokyo
msgid "Asia/Tokyo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/tomsk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/tomsk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/tomsk
msgid "Asia/Tomsk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/ujung_pandang
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/ujung_pandang
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/ujung_pandang
msgid "Asia/Ujung_Pandang"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/ulaanbaatar
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/ulaanbaatar
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/ulaanbaatar
msgid "Asia/Ulaanbaatar"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/ulan_bator
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/ulan_bator
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/ulan_bator
msgid "Asia/Ulan_Bator"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/urumqi
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/urumqi
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/urumqi
msgid "Asia/Urumqi"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/ust-nera
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/ust-nera
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/ust-nera
msgid "Asia/Ust-Nera"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/vientiane
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/vientiane
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/vientiane
msgid "Asia/Vientiane"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/vladivostok
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/vladivostok
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/vladivostok
msgid "Asia/Vladivostok"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/yakutsk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/yakutsk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/yakutsk
msgid "Asia/Yakutsk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/yangon
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/yangon
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/yangon
msgid "Asia/Yangon"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/yekaterinburg
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/yekaterinburg
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/yekaterinburg
msgid "Asia/Yekaterinburg"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__asia/yerevan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__asia/yerevan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__asia/yerevan
msgid "Asia/Yerevan"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_associated_product_line
msgid "Associated Product"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__start_round_rule_days
msgid ""
"At which time daily services should start (defined in working calendar time "
"zone)"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/azores
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/azores
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/azores
msgid "Atlantic/Azores"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/bermuda
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/bermuda
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/bermuda
msgid "Atlantic/Bermuda"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/canary
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/canary
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/canary
msgid "Atlantic/Canary"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/cape_verde
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/cape_verde
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/cape_verde
msgid "Atlantic/Cape_Verde"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/faeroe
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/faeroe
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/faeroe
msgid "Atlantic/Faeroe"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/faroe
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/faroe
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/faroe
msgid "Atlantic/Faroe"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/jan_mayen
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/jan_mayen
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/jan_mayen
msgid "Atlantic/Jan_Mayen"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/madeira
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/madeira
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/madeira
msgid "Atlantic/Madeira"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/reykjavik
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/reykjavik
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/reykjavik
msgid "Atlantic/Reykjavik"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/south_georgia
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/south_georgia
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/south_georgia
msgid "Atlantic/South_Georgia"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/st_helena
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/st_helena
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/st_helena
msgid "Atlantic/St_Helena"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__atlantic/stanley
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__atlantic/stanley
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__atlantic/stanley
msgid "Atlantic/Stanley"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_attachment_count
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_attachment_count
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_attachment_count
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_attachment_count
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/act
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/act
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/act
msgid "Australia/ACT"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/adelaide
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/adelaide
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/adelaide
msgid "Australia/Adelaide"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/brisbane
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/brisbane
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/brisbane
msgid "Australia/Brisbane"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/broken_hill
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/broken_hill
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/broken_hill
msgid "Australia/Broken_Hill"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/canberra
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/canberra
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/canberra
msgid "Australia/Canberra"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/currie
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/currie
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/currie
msgid "Australia/Currie"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/darwin
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/darwin
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/darwin
msgid "Australia/Darwin"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/eucla
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/eucla
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/eucla
msgid "Australia/Eucla"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/hobart
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/hobart
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/hobart
msgid "Australia/Hobart"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/lhi
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/lhi
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/lhi
msgid "Australia/LHI"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/lindeman
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/lindeman
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/lindeman
msgid "Australia/Lindeman"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/lord_howe
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/lord_howe
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/lord_howe
msgid "Australia/Lord_Howe"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/melbourne
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/melbourne
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/melbourne
msgid "Australia/Melbourne"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/nsw
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/nsw
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/nsw
msgid "Australia/NSW"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/north
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/north
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/north
msgid "Australia/North"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/perth
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/perth
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/perth
msgid "Australia/Perth"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/queensland
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/queensland
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/queensland
msgid "Australia/Queensland"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/south
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/south
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/south
msgid "Australia/South"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/sydney
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/sydney
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/sydney
msgid "Australia/Sydney"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/tasmania
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/tasmania
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/tasmania
msgid "Australia/Tasmania"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/victoria
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/victoria
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/victoria
msgid "Australia/Victoria"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/west
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/west
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/west
msgid "Australia/West"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__australia/yancowinna
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__australia/yancowinna
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__australia/yancowinna
msgid "Australia/Yancowinna"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__ba_auto_sale_order__confirmed
msgid "Auto Confirmed Sale Order"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__ba_auto_sale_order__draft
msgid "Auto Draft Sale Order"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__ba_auto_sale_order
msgid "Auto Sale Order"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allocation_type__automatic
msgid "Automatic"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
msgid "Automatic Resource Allocation"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__service_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__service_ids
msgid "Available Services"
msgstr "مقدمين الخدمات المتاحة"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__available_service_ids
msgid "Available services"
msgstr "مقدمين الخدمات المتاحة"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__rating_satisfaction
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__rating_satisfaction
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__rating_satisfaction
msgid "Average Rating"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment_core.py:0
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#, python-format
msgid "Awaiting Confirmation"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__brazil/acre
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__brazil/acre
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__brazil/acre
msgid "Brazil/Acre"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__brazil/denoronha
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__brazil/denoronha
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__brazil/denoronha
msgid "Brazil/DeNoronha"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__brazil/east
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__brazil/east
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__brazil/east
msgid "Brazil/East"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__brazil/west
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__brazil/west
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__brazil/west
msgid "Brazil/West"
msgstr ""

#. module: business_appointment
#: model:ir.module.category,name:business_appointment.module_category_business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_calendar
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Business Appointments"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Business Appointments Options"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_business_resource
msgid "Business Resource"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_business_resource_type
msgid "Business Resource Type"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allocation_method__by_number
msgid "By Appointments Number"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allocation_method__by_order
msgid "By Order"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allocation_method__by_workload
msgid "By Workload"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__cet
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__cet
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__cet
msgid "CET"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__cst6cdt
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__cst6cdt
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__cst6cdt
msgid "CST6CDT"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__pricing_method
msgid "Calculate price for"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_partner__ba_calendar_defaults
#: model:ir.model.fields,field_description:business_appointment.field_res_users__ba_calendar_defaults
msgid "Calendar Defaults"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__calendar_event_workload
msgid "Calendar Events as Busy Time"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__canada/atlantic
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__canada/atlantic
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__canada/atlantic
msgid "Canada/Atlantic"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__canada/central
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__canada/central
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__canada/central
msgid "Canada/Central"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__canada/eastern
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__canada/eastern
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__canada/eastern
msgid "Canada/Eastern"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__canada/mountain
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__canada/mountain
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__canada/mountain
msgid "Canada/Mountain"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__canada/newfoundland
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__canada/newfoundland
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__canada/newfoundland
msgid "Canada/Newfoundland"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__canada/pacific
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__canada/pacific
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__canada/pacific
msgid "Canada/Pacific"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__canada/saskatchewan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__canada/saskatchewan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__canada/saskatchewan
msgid "Canada/Saskatchewan"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__canada/yukon
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__canada/yukon
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__canada/yukon
msgid "Canada/Yukon"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/business_appointment_calendarcontroller.js:0
#: code:addons/business_appointment/static/src/js/business_appointment_formcontroller.js:0
#: code:addons/business_appointment/static/src/js/business_appointment_list_controller.js:0
#: code:addons/business_appointment/static/src/js/time_slots.js:0
#: model:ir.actions.server,name:business_appointment.action_mass_appointments_cancel
#: model:ir.actions.server,name:business_appointment.action_mass_appointments_rcancel
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_form
#, python-format
msgid "Cancel"
msgstr ""

#. module: business_appointment
#: model:ir.actions.server,name:business_appointment.cron_clean_prereservation_ir_actions_server
#: model:ir.cron,cron_name:business_appointment.cron_clean_prereservation
#: model:ir.cron,name:business_appointment.cron_clean_prereservation
msgid "Cancel expired pre-reservations"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment.py:0
#, python-format
msgid "Canceled"
msgstr ""

#. module: business_appointment
#: model:mail.message.subtype,description:business_appointment.mt_business_appointment_cancel
#: model:mail.message.subtype,description:business_appointment.mt_business_resource_cancel
#: model:mail.message.subtype,description:business_appointment.mt_business_resource_type_cancel
msgid "Cancellation"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__chile/continental
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__chile/continental
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__chile/continental
msgid "Chile/Continental"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__chile/easterisland
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__chile/easterisland
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__chile/easterisland
msgid "Chile/EasterIsland"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/time_slots.js:0
#, python-format
msgid "Choose Contact and Confirm"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__city
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__city
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__city
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__city
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.choose_appointment_customert_form_view
msgid "City"
msgstr ""

#. module: business_appointment
#: model_terms:ir.actions.act_window,help:business_appointment.appointment_product_action
msgid "Click 'Create' to add a new service used for appointments"
msgstr "انقر فوق "إنشاء" لإضافة خدمة جديدة تستخدم للمواعيد"

#. module: business_appointment
#: model_terms:ir.actions.act_window,help:business_appointment.business_resource_type_action
msgid ""
"Click 'Create' to add new resource types, e.g. 'Therapists', 'Conference "
"Rooms', 'Factory Equipment'"
msgstr ""

#. module: business_appointment
#: model_terms:ir.actions.act_window,help:business_appointment.business_resource_action
msgid ""
"Click 'Create' to add new resource, e.g. 'Doctor Brown', 'Room 212', "
"'Milling machine 909'"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__color
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__color
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__color
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__color
msgid "Color"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__company_id
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__company_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__company_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__company_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__company_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__company_id
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
msgid "Company"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__partner_name
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__partner_name
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__partner_name
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__partner_name
msgid "Company Name"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__appoin_comp_tz
msgid "Company Timezone"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/slots_widget_core.js:0
#: code:addons/business_appointment/static/src/xml/time_slots.xml:0
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__suggested_product_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__extra_product_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__extra_product_ids
#, python-format
msgid "Complementary Products"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__ba_extra_products_backend
msgid "Complementary Products Offer"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الاعدادات"

#. module: business_appointment
#: model:ir.ui.menu,name:business_appointment.menu_business_appointments_conf
#: model:ir.ui.menu,name:business_appointment.menu_res_config_settings_appointment
msgid "Configuration"
msgstr "إعدادات التكوين"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__safe_file_name
msgid "Confimration File Name"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/time_slots.js:0
#: model:ir.actions.server,name:business_appointment.action_mass_appointments_confirmation
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#, python-format
msgid "Confirm"
msgstr ""

#. module: business_appointment
#: model:ir.actions.report,name:business_appointment.action_report_business_appointment
msgid "Confirmation"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_res_partner
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__partner_id
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__partner_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__partner_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__partner_id
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__partner_id
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
msgid "Contact"
msgstr "جهة الاتصال"

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_appointment_contact_info
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
msgid "Contact Info"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__contact_name
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__contact_name
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__contact_name
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__contact_name
msgid "Contact Name"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__country_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__country_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__country_id
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__country_id
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.choose_appointment_customert_form_view
msgid "Country"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_alarm_task__create_uid
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__create_uid
#: model:ir.model.fields,field_description:business_appointment.field_appointment_group__create_uid
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__create_uid
#: model:ir.model.fields,field_description:business_appointment.field_associated_product_line__create_uid
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__create_uid
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__create_uid
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_custom_search__create_uid
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__create_uid
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__create_uid
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__create_uid
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__create_uid
msgid "Created by"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_alarm_task__create_date
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__create_date
#: model:ir.model.fields,field_description:business_appointment.field_appointment_group__create_date
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__create_date
#: model:ir.model.fields,field_description:business_appointment.field_associated_product_line__create_date
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__create_date
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__create_date
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_custom_search__create_date
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__create_date
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__create_date
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__create_date
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__create_date
msgid "Created on"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.rating_rating_view_search_resource_type
msgid "Creation Date"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__cuba
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__cuba
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__cuba
msgid "Cuba"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__module_business_appointment_custom_fields
msgid "Custom Fields"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_business_appointment_custom_search
msgid "Custom Search"
msgstr ""

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.rating_rating_action_business_resource_report
#: model:ir.ui.menu,name:business_appointment.menu_business_ratings
msgid "Customer Ratings"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
msgid "Date"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__day_date
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__day_date
msgid "Day"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_alarm__duration_uom__days
msgid "Day(s)"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__default_alarm_ids
msgid "Default Alarms"
msgstr "الإنذارات الافتراضية"

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__resource_calendar_id
#: model:ir.model.fields,help:business_appointment.field_business_resource__resource_calendar_id
msgid "Define the schedule of resource"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Define time zone to show time slots"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_res_config_settings__ba_auto_sale_order
msgid ""
"Define wheter sale order should be auto created / confirmed, when "
"appointment is confirmed"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__duration_uom
#: model:ir.model.fields,help:business_appointment.field_make_business_appointment__duration_uom
msgid ""
"Define whether appointments should be scheduled for hours or for days. In the latter case make sure work\n"
"        times allow 24-hours periods (should start at 00:00 and end at 24:00)"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Define whether website / portal visitor should confirm appointment"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_kanban
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_kanban
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_kanban
msgid "Delete"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__description
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__description
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_form
msgid "Description"
msgstr "الوصف"

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/ba_popups.js:0
#, python-format
msgid "Details"
msgstr "التفاصيل"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__business_appointment_timezone_option
msgid "Different Time Zones"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_alarm_task__display_name
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__display_name
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__display_name
#: model:ir.model.fields,field_description:business_appointment.field_appointment_group__display_name
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__display_name
#: model:ir.model.fields,field_description:business_appointment.field_associated_product_line__display_name
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__display_name
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__display_name
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_custom_search__display_name
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__display_name
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__display_name
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__display_name
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__display_name
msgid "Display Name"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment.py:0
#, python-format
msgid "Done"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
msgid "Done Appointments"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__duration_minutes
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__duration
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__duration
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__duration
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__duration
msgid "Duration"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__duration_uom
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__duration_uom
msgid "Duration UoM"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__appointment_duration_days
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__duration_days
msgid "Duration of Appointment"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/appointment_product.py:0
#, python-format
msgid "Duration should be positive"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/appointment_alarm.py:0
#: model:ir.model.constraint,message:business_appointment.constraint_appointment_alarm_allowed_duration
#, python-format
msgid "Duration should be positive!"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__eet
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__eet
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__eet
msgid "EET"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__est
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__est
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__est
msgid "EST"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__est5edt
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__est5edt
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__est5edt
msgid "EST5EDT"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_kanban
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_kanban
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_kanban
msgid "Edit"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__egypt
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__egypt
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__egypt
msgid "Egypt"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__eire
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__eire
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__eire
msgid "Eire"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment_core.py:0
#: code:addons/business_appointment/models/business_appointment_core.py:0
#, python-format
msgid "Either contact or contact name should be defined!"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__email
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__email
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__email
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__email
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_alarm__ttype__email
msgid "Email"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__ba_approval_type__email
msgid "Email Confirmation"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__mail_template_id
msgid "Email Template"
msgstr ""

#. module: business_appointment
#: model:ir.ui.menu,name:business_appointment.menu_appointments_email_templates
msgid "Email Templates"
msgstr "قوالب البريد الإلكتروني"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__module_business_appointment_hr
msgid "Employees as Resources"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt
msgid "Etc/GMT"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+0
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+0
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+0
msgid "Etc/GMT+0"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+1
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+1
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+1
msgid "Etc/GMT+1"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+10
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+10
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+10
msgid "Etc/GMT+10"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+11
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+11
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+11
msgid "Etc/GMT+11"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+12
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+12
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+12
msgid "Etc/GMT+12"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+2
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+2
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+2
msgid "Etc/GMT+2"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+3
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+3
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+3
msgid "Etc/GMT+3"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+4
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+4
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+4
msgid "Etc/GMT+4"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+5
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+5
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+5
msgid "Etc/GMT+5"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+6
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+6
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+6
msgid "Etc/GMT+6"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+7
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+7
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+7
msgid "Etc/GMT+7"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+8
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+8
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+8
msgid "Etc/GMT+8"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt+9
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt+9
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt+9
msgid "Etc/GMT+9"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-0
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-0
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-0
msgid "Etc/GMT-0"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-1
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-1
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-1
msgid "Etc/GMT-1"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-10
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-10
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-10
msgid "Etc/GMT-10"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-11
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-11
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-11
msgid "Etc/GMT-11"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-12
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-12
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-12
msgid "Etc/GMT-12"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-13
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-13
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-13
msgid "Etc/GMT-13"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-14
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-14
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-14
msgid "Etc/GMT-14"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-2
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-2
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-2
msgid "Etc/GMT-2"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-3
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-3
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-3
msgid "Etc/GMT-3"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-4
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-4
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-4
msgid "Etc/GMT-4"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-5
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-5
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-5
msgid "Etc/GMT-5"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-6
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-6
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-6
msgid "Etc/GMT-6"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-7
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-7
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-7
msgid "Etc/GMT-7"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-8
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-8
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-8
msgid "Etc/GMT-8"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt-9
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt-9
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt-9
msgid "Etc/GMT-9"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/gmt0
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/gmt0
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/gmt0
msgid "Etc/GMT0"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/greenwich
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/greenwich
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/greenwich
msgid "Etc/Greenwich"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/uct
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/uct
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/uct
msgid "Etc/UCT"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/utc
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/utc
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/utc
msgid "Etc/UTC"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/universal
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/universal
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/universal
msgid "Etc/Universal"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__etc/zulu
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__etc/zulu
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__etc/zulu
msgid "Etc/Zulu"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/amsterdam
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/amsterdam
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/amsterdam
msgid "Europe/Amsterdam"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/andorra
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/andorra
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/andorra
msgid "Europe/Andorra"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/astrakhan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/astrakhan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/astrakhan
msgid "Europe/Astrakhan"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/athens
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/athens
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/athens
msgid "Europe/Athens"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/belfast
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/belfast
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/belfast
msgid "Europe/Belfast"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/belgrade
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/belgrade
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/belgrade
msgid "Europe/Belgrade"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/berlin
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/berlin
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/berlin
msgid "Europe/Berlin"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/bratislava
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/bratislava
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/bratislava
msgid "Europe/Bratislava"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/brussels
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/brussels
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/brussels
msgid "Europe/Brussels"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/bucharest
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/bucharest
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/bucharest
msgid "Europe/Bucharest"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/budapest
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/budapest
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/budapest
msgid "Europe/Budapest"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/busingen
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/busingen
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/busingen
msgid "Europe/Busingen"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/chisinau
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/chisinau
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/chisinau
msgid "Europe/Chisinau"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/copenhagen
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/copenhagen
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/copenhagen
msgid "Europe/Copenhagen"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/dublin
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/dublin
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/dublin
msgid "Europe/Dublin"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/gibraltar
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/gibraltar
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/gibraltar
msgid "Europe/Gibraltar"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/guernsey
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/guernsey
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/guernsey
msgid "Europe/Guernsey"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/helsinki
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/helsinki
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/helsinki
msgid "Europe/Helsinki"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/isle_of_man
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/isle_of_man
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/isle_of_man
msgid "Europe/Isle_of_Man"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/istanbul
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/istanbul
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/istanbul
msgid "Europe/Istanbul"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/jersey
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/jersey
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/jersey
msgid "Europe/Jersey"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/kaliningrad
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/kaliningrad
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/kaliningrad
msgid "Europe/Kaliningrad"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/kiev
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/kiev
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/kiev
msgid "Europe/Kiev"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/kirov
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/kirov
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/kirov
msgid "Europe/Kirov"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/lisbon
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/lisbon
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/lisbon
msgid "Europe/Lisbon"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/ljubljana
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/ljubljana
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/ljubljana
msgid "Europe/Ljubljana"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/london
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/london
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/london
msgid "Europe/London"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/luxembourg
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/luxembourg
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/luxembourg
msgid "Europe/Luxembourg"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/madrid
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/madrid
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/madrid
msgid "Europe/Madrid"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/malta
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/malta
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/malta
msgid "Europe/Malta"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/mariehamn
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/mariehamn
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/mariehamn
msgid "Europe/Mariehamn"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/minsk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/minsk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/minsk
msgid "Europe/Minsk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/monaco
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/monaco
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/monaco
msgid "Europe/Monaco"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/moscow
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/moscow
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/moscow
msgid "Europe/Moscow"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/nicosia
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/nicosia
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/nicosia
msgid "Europe/Nicosia"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/oslo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/oslo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/oslo
msgid "Europe/Oslo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/paris
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/paris
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/paris
msgid "Europe/Paris"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/podgorica
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/podgorica
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/podgorica
msgid "Europe/Podgorica"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/prague
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/prague
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/prague
msgid "Europe/Prague"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/riga
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/riga
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/riga
msgid "Europe/Riga"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/rome
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/rome
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/rome
msgid "Europe/Rome"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/samara
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/samara
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/samara
msgid "Europe/Samara"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/san_marino
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/san_marino
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/san_marino
msgid "Europe/San_Marino"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/sarajevo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/sarajevo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/sarajevo
msgid "Europe/Sarajevo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/saratov
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/saratov
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/saratov
msgid "Europe/Saratov"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/simferopol
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/simferopol
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/simferopol
msgid "Europe/Simferopol"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/skopje
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/skopje
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/skopje
msgid "Europe/Skopje"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/sofia
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/sofia
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/sofia
msgid "Europe/Sofia"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/stockholm
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/stockholm
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/stockholm
msgid "Europe/Stockholm"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/tallinn
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/tallinn
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/tallinn
msgid "Europe/Tallinn"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/tirane
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/tirane
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/tirane
msgid "Europe/Tirane"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/tiraspol
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/tiraspol
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/tiraspol
msgid "Europe/Tiraspol"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/ulyanovsk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/ulyanovsk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/ulyanovsk
msgid "Europe/Ulyanovsk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/uzhgorod
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/uzhgorod
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/uzhgorod
msgid "Europe/Uzhgorod"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/vaduz
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/vaduz
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/vaduz
msgid "Europe/Vaduz"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/vatican
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/vatican
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/vatican
msgid "Europe/Vatican"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/vienna
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/vienna
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/vienna
msgid "Europe/Vienna"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/vilnius
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/vilnius
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/vilnius
msgid "Europe/Vilnius"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/volgograd
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/volgograd
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/volgograd
msgid "Europe/Volgograd"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/warsaw
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/warsaw
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/warsaw
msgid "Europe/Warsaw"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/zagreb
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/zagreb
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/zagreb
msgid "Europe/Zagreb"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/zaporozhye
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/zaporozhye
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/zaporozhye
msgid "Europe/Zaporozhye"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__europe/zurich
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__europe/zurich
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__europe/zurich
msgid "Europe/Zurich"
msgstr ""

#. module: business_appointment
#: model:mail.message.subtype,name:business_appointment.mt_reminder_external
msgid "External Reminder"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__extra_working_calendar_id
msgid "Extra Calendar Restriction"
msgstr "قيود التقويم الإضافية"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Extra Features"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_form
msgid "Extra Products"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.report_business_appointment_single
msgid "Extra products"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_custom_search__custom_field_id
msgid "Field"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_choose_appointment_customer
msgid "Finish Scheduling"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_follower_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_follower_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_follower_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_follower_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_follower_ids
msgid "Followers"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_partner_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_partner_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_partner_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_partner_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__activity_type_icon
#: model:ir.model.fields,help:business_appointment.field_business_appointment__activity_type_icon
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__activity_type_icon
#: model:ir.model.fields,help:business_appointment.field_business_resource__activity_type_icon
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/time_slots.xml:0
#, python-format
msgid "Forward"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__allowed_from_uom
msgid "From UoM"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "Future Activities"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__gb
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__gb
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__gb
msgid "GB"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__gb-eire
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__gb-eire
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__gb-eire
msgid "GB-Eire"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__gmt
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__gmt
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__gmt
msgid "GMT"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__gmt+0
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__gmt+0
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__gmt+0
msgid "GMT+0"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__gmt-0
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__gmt-0
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__gmt-0
msgid "GMT-0"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__gmt0
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__gmt0
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__gmt0
msgid "GMT0"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__greenwich
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__greenwich
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__greenwich
msgid "Greenwich"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "Group By"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.alarm_task_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_alarm_view_search
msgid "Group by..."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__hst
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__hst
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__hst
msgid "HST"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__has_message
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__has_message
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__has_message
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__has_message
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__has_message
msgid "Has Message"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__hongkong
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__hongkong
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__hongkong
msgid "Hongkong"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_alarm__duration_uom__hours
msgid "Hour(s)"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__multiple_manual_duration
msgid ""
"How manual duration should be rounded. E.g. 0:15 means rounding for 15 "
"minutes: 0:17 > 0:30"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__multiple_manual_duration_days
msgid ""
"How manual duration should be rounded. E.g. 2 means rounding for 2 days: 3 >"
" 4"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_alarm_task__id
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__id
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__id
#: model:ir.model.fields,field_description:business_appointment.field_appointment_group__id
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__id
#: model:ir.model.fields,field_description:business_appointment.field_associated_product_line__id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_custom_search__id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__id
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__id
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__id
msgid "ID"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__iceland
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__iceland
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__iceland
msgid "Iceland"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__activity_exception_icon
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__activity_exception_icon
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__activity_exception_icon
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__activity_exception_icon
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__activity_exception_icon
#: model:ir.model.fields,help:business_appointment.field_business_appointment__activity_exception_icon
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__activity_exception_icon
#: model:ir.model.fields,help:business_appointment.field_business_resource__activity_exception_icon
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "If checked a few time slots might be chosen during scheduling"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__calendar_event_workload
msgid ""
"If checked, for periods when a responsible user has meetings, appointments "
"would not be possible"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"If checked, it would be possible to create pricelist and assign different prices\n"
"                                    per products"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__message_needaction
#: model:ir.model.fields,help:business_appointment.field_appointment_product__message_unread
#: model:ir.model.fields,help:business_appointment.field_business_appointment__message_needaction
#: model:ir.model.fields,help:business_appointment.field_business_appointment__message_unread
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__message_needaction
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__message_unread
#: model:ir.model.fields,help:business_appointment.field_business_resource__message_needaction
#: model:ir.model.fields,help:business_appointment.field_business_resource__message_unread
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__message_needaction
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__message_has_error
#: model:ir.model.fields,help:business_appointment.field_appointment_product__message_has_sms_error
#: model:ir.model.fields,help:business_appointment.field_business_appointment__message_has_error
#: model:ir.model.fields,help:business_appointment.field_business_appointment__message_has_sms_error
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__message_has_error
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__message_has_sms_error
#: model:ir.model.fields,help:business_appointment.field_business_resource__message_has_error
#: model:ir.model.fields,help:business_appointment.field_business_resource__message_has_sms_error
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__message_has_error
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__rating_mail_template_id
msgid "If not defined, standard 'Appointments: Rating Template' would be used"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__success_mail_template_id
msgid ""
"If not defined, standard 'Appointments: Success Email Template' would be "
"used"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__appointment_duration
#: model:ir.model.fields,help:business_appointment.field_appointment_product__appointment_duration_days
#: model:ir.model.fields,help:business_appointment.field_make_business_appointment__duration
#: model:ir.model.fields,help:business_appointment.field_make_business_appointment__duration_days
msgid "If not stated, the duration would be considered an one"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_res_config_settings__ba_approval_type
msgid "If sms is chosen but not available, then email would be used."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__image_1920
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__image_1920
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__image_1920
msgid "Image"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__image_1024
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__image_1024
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__image_1024
msgid "Image 1024"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__image_128
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__image_128
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__image_128
msgid "Image 128"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__image_256
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__image_256
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__image_256
msgid "Image 256"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__image_512
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__image_512
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__image_512
msgid "Image 512"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__allocation_method
msgid ""
"In case of automatic resource selection or in case of manual selection of a few resources, there might \n"
"        be the situation when the same time slots relate to the same resource.\n"
"        This setting defines which resource should be taken:\n"
"         * By order: a resource with the highest sequence would be taken. Use drag&drop on resources \n"
"               list view. Thus, the most prioritized resources would have the highest workload.\n"
"         * By Appointments Number: a resource with the lowest number of open appointments would be taken\n"
"         * By Workload: a resource with the lowest duration of open appointments would be taken\n"
"        "
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__indian/antananarivo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__indian/antananarivo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__indian/antananarivo
msgid "Indian/Antananarivo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__indian/chagos
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__indian/chagos
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__indian/chagos
msgid "Indian/Chagos"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__indian/christmas
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__indian/christmas
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__indian/christmas
msgid "Indian/Christmas"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__indian/cocos
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__indian/cocos
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__indian/cocos
msgid "Indian/Cocos"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__indian/comoro
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__indian/comoro
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__indian/comoro
msgid "Indian/Comoro"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__indian/kerguelen
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__indian/kerguelen
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__indian/kerguelen
msgid "Indian/Kerguelen"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__indian/mahe
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__indian/mahe
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__indian/mahe
msgid "Indian/Mahe"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__indian/maldives
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__indian/maldives
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__indian/maldives
msgid "Indian/Maldives"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__indian/mauritius
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__indian/mauritius
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__indian/mauritius
msgid "Indian/Mauritius"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__indian/mayotte
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__indian/mayotte
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__indian/mayotte
msgid "Indian/Mayotte"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__indian/reunion
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__indian/reunion
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__indian/reunion
msgid "Indian/Reunion"
msgstr ""

#. module: business_appointment
#: model:mail.message.subtype,name:business_appointment.mt_reminder_internal
msgid "Internal Reminder"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__duration_uom
msgid "Interval"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__iran
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__iran
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__iran
msgid "Iran"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_is_follower
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_is_follower
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_is_follower
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_is_follower
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__israel
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__israel
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__israel
msgid "Israel"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__jamaica
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__jamaica
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__jamaica
msgid "Jamaica"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__japan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__japan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__japan
msgid "Japan"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__function
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__function
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__function
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__function
msgid "Job Position"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__kwajalein
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__kwajalein
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__kwajalein
msgid "Kwajalein"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_custom_search__name
msgid "Label"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__lang
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__lang
msgid "Language"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_alarm_task____last_update
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm____last_update
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic____last_update
#: model:ir.model.fields,field_description:business_appointment.field_appointment_group____last_update
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product____last_update
#: model:ir.model.fields,field_description:business_appointment.field_associated_product_line____last_update
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment____last_update
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core____last_update
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_custom_search____last_update
#: model:ir.model.fields,field_description:business_appointment.field_business_resource____last_update
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type____last_update
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer____last_update
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment____last_update
msgid "Last Modified on"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_alarm_task__write_uid
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__write_uid
#: model:ir.model.fields,field_description:business_appointment.field_appointment_group__write_uid
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__write_uid
#: model:ir.model.fields,field_description:business_appointment.field_associated_product_line__write_uid
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__write_uid
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__write_uid
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_custom_search__write_uid
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__write_uid
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__write_uid
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__write_uid
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__write_uid
msgid "Last Updated by"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_alarm_task__write_date
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__write_date
#: model:ir.model.fields,field_description:business_appointment.field_appointment_group__write_date
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__write_date
#: model:ir.model.fields,field_description:business_appointment.field_associated_product_line__write_date
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__write_date
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__write_date
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_custom_search__write_date
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__write_date
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__write_date
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__write_date
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__write_date
msgid "Last Updated on"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "Late Activities"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__late_to_know
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__late_to_know
msgid "Late but yet Planned"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__libya
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__libya
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__libya
msgid "Libya"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__module_business_appointment_sale
msgid "Link Appointments to Sale Orders"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__location
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__location
msgid "Location"
msgstr "الموقع"

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__met
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__met
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__met
msgid "MET"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__mst
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__mst
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__mst
msgid "MST"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__mst7mdt
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__mst7mdt
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__mst7mdt
msgid "MST7MDT"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_main_attachment_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_main_attachment_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_main_attachment_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_main_attachment_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/buttons.xml:0
#, python-format
msgid "Main actions"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"Make time zone available for selection while scheduling an appointment.\n"
"                                    Otherwise, all time slots would be shown always in the same timezone taken from a\n"
"                                    current company."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allocation_type__manual
msgid "Manual"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
msgid "Manual Resource Allocation"
msgstr ""

#. module: business_appointment
#: model:ir.actions.server,name:business_appointment.action_mass_appointments_done
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_form
msgid "Mark Done"
msgstr ""

#. module: business_appointment
#: model:ir.actions.server,name:business_appointment.action_mass_appointments_missed
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_form
msgid "Mark Missed"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__max_manual_duration_days
msgid "Max Duration Days"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__max_manual_duration
msgid "Max Duration Hours"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__ba_max_multi_scheduling
msgid "Maximum Appointments (Backend)"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__ba_max_approval_trials
msgid "Maximum Number of Attempts to Confirm"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__ba_confirmation_retry_trials
msgid "Maximum Number of Code Refreshing"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__ba_max_approval_time
msgid "Maximum Period for Confirmation (h.)"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__ba_max_preresevation_time
msgid "Maximum Period for Pre-Reservation"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/slots_widget_core.js:0
#, python-format
msgid "Maximum number of appointments"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_has_error
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_has_error
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_has_error
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_has_error
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_ids
msgid "Messages"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__mexico/bajanorte
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__mexico/bajanorte
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__mexico/bajanorte
msgid "Mexico/BajaNorte"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__mexico/bajasur
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__mexico/bajasur
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__mexico/bajasur
msgid "Mexico/BajaSur"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__mexico/general
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__mexico/general
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__mexico/general
msgid "Mexico/General"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__min_manual_duration_days
msgid "Min Duration Days"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__min_manual_duration
msgid "Min Duration Hours"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/appointment_product.py:0
#, python-format
msgid "Min Duration should be less than Max Duration. Both should be positive"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_alarm__duration_uom__minutes
msgid "Minute(s)"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment.py:0
#, python-format
msgid "Missed"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__mobile
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__mobile
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__mobile
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__mobile
msgid "Mobile"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_custom_search__model
msgid "Model"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__day_month
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__day_month
msgid "Month"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__ba_multi_scheduling
msgid "Multi Scheduling"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Multiple Services Prices"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__multiple_manual_duration_days
msgid "Multiple for Days"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__multiple_manual_duration
msgid "Multiple for Hours"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/appointment_product.py:0
#, python-format
msgid "Multiple should positive and less than max"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "My"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__my_activity_date_deadline
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__my_activity_date_deadline
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__my_activity_date_deadline
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__my_activity_date_deadline
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
msgid "My Appointments"
msgstr "المواعيد الخاصة بي"

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__nz
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__nz
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__nz
msgid "NZ"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__nz-chat
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__nz-chat
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__nz-chat
msgid "NZ-CHAT"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__name
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__name
msgid "Name"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__navajo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__navajo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__navajo
msgid "Navajo"
msgstr ""

#. module: business_appointment
#: model:mail.message.subtype,name:business_appointment.mt_business_appointment_new
#: model:mail.message.subtype,name:business_appointment.mt_business_resource_new
#: model:mail.message.subtype,name:business_appointment.mt_business_resource_type_new
msgid "New Appointments and Recovering"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__ba_confirmation_retry_period
msgid "New Confirmation Code Minimum Period (s.)"
msgstr ""

#. module: business_appointment
#: model:mail.message.subtype,description:business_appointment.mt_business_appointment_new
#: model:mail.message.subtype,description:business_appointment.mt_business_resource_new
#: model:mail.message.subtype,description:business_appointment.mt_business_resource_type_new
msgid "New appointment"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
msgid "Next 7 days"
msgstr "خلال الاسبوع القادم"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__activity_calendar_event_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__activity_calendar_event_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__activity_calendar_event_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__activity_calendar_event_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__activity_date_deadline
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__activity_date_deadline
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__activity_date_deadline
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__activity_date_deadline
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__activity_summary
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__activity_summary
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__activity_summary
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__activity_summary
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__activity_type_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__activity_type_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__activity_type_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__activity_type_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__ba_auto_sale_order__no
msgid "No Auto Creation"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__ba_approval_type__no
msgid "No Confirmation"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/time_slots.xml:0
#, python-format
msgid "No appointments are possible. Try to select different dates"
msgstr ""

#. module: business_appointment
#: model_terms:ir.actions.act_window,help:business_appointment.rating_rating_action_appointment_product
#: model_terms:ir.actions.act_window,help:business_appointment.rating_rating_action_business_appointment
#: model_terms:ir.actions.act_window,help:business_appointment.rating_rating_action_business_resource
#: model_terms:ir.actions.act_window,help:business_appointment.rating_rating_action_business_resource_report
msgid "No customer ratings yet"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__description
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__description
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__description
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__description
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_form
msgid "Notes"
msgstr ""

#. module: business_appointment
#: model:ir.ui.menu,name:business_appointment.menu_ba_templates
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_form
msgid "Notifications"
msgstr "إشعارات"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_group__appointment_len
msgid "Number"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_needaction_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_needaction_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_needaction_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_needaction_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__appointment_len
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__appointment_len
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__appointment_len
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__number_of_appointments
msgid "Number of appointments"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_has_error_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_has_error_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_has_error_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_has_error_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__message_needaction_counter
#: model:ir.model.fields,help:business_appointment.field_business_appointment__message_needaction_counter
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__message_needaction_counter
#: model:ir.model.fields,help:business_appointment.field_business_resource__message_needaction_counter
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__message_has_error_counter
#: model:ir.model.fields,help:business_appointment.field_business_appointment__message_has_error_counter
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__message_has_error_counter
#: model:ir.model.fields,help:business_appointment.field_business_resource__message_has_error_counter
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__resource_len
msgid "Number of resources"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__message_unread_counter
#: model:ir.model.fields,help:business_appointment.field_business_appointment__message_unread_counter
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__message_unread_counter
#: model:ir.model.fields,help:business_appointment.field_business_resource__message_unread_counter
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/ba_popups.js:0
#, python-format
msgid "OK"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_alarm__recipients__user_id
msgid "Only responsible"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_form
msgid "Own Leaves"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__prc
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__prc
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__prc
msgid "PRC"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pst8pdt
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pst8pdt
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pst8pdt
msgid "PST8PDT"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/apia
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/apia
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/apia
msgid "Pacific/Apia"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/auckland
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/auckland
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/auckland
msgid "Pacific/Auckland"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/bougainville
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/bougainville
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/bougainville
msgid "Pacific/Bougainville"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/chatham
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/chatham
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/chatham
msgid "Pacific/Chatham"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/chuuk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/chuuk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/chuuk
msgid "Pacific/Chuuk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/easter
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/easter
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/easter
msgid "Pacific/Easter"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/efate
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/efate
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/efate
msgid "Pacific/Efate"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/enderbury
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/enderbury
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/enderbury
msgid "Pacific/Enderbury"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/fakaofo
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/fakaofo
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/fakaofo
msgid "Pacific/Fakaofo"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/fiji
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/fiji
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/fiji
msgid "Pacific/Fiji"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/funafuti
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/funafuti
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/funafuti
msgid "Pacific/Funafuti"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/galapagos
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/galapagos
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/galapagos
msgid "Pacific/Galapagos"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/gambier
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/gambier
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/gambier
msgid "Pacific/Gambier"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/guadalcanal
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/guadalcanal
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/guadalcanal
msgid "Pacific/Guadalcanal"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/guam
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/guam
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/guam
msgid "Pacific/Guam"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/honolulu
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/honolulu
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/honolulu
msgid "Pacific/Honolulu"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/johnston
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/johnston
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/johnston
msgid "Pacific/Johnston"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/kiritimati
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/kiritimati
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/kiritimati
msgid "Pacific/Kiritimati"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/kosrae
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/kosrae
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/kosrae
msgid "Pacific/Kosrae"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/kwajalein
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/kwajalein
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/kwajalein
msgid "Pacific/Kwajalein"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/majuro
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/majuro
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/majuro
msgid "Pacific/Majuro"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/marquesas
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/marquesas
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/marquesas
msgid "Pacific/Marquesas"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/midway
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/midway
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/midway
msgid "Pacific/Midway"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/nauru
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/nauru
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/nauru
msgid "Pacific/Nauru"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/niue
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/niue
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/niue
msgid "Pacific/Niue"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/norfolk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/norfolk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/norfolk
msgid "Pacific/Norfolk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/noumea
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/noumea
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/noumea
msgid "Pacific/Noumea"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/pago_pago
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/pago_pago
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/pago_pago
msgid "Pacific/Pago_Pago"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/palau
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/palau
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/palau
msgid "Pacific/Palau"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/pitcairn
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/pitcairn
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/pitcairn
msgid "Pacific/Pitcairn"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/pohnpei
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/pohnpei
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/pohnpei
msgid "Pacific/Pohnpei"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/ponape
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/ponape
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/ponape
msgid "Pacific/Ponape"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/port_moresby
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/port_moresby
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/port_moresby
msgid "Pacific/Port_Moresby"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/rarotonga
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/rarotonga
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/rarotonga
msgid "Pacific/Rarotonga"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/saipan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/saipan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/saipan
msgid "Pacific/Saipan"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/samoa
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/samoa
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/samoa
msgid "Pacific/Samoa"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/tahiti
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/tahiti
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/tahiti
msgid "Pacific/Tahiti"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/tarawa
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/tarawa
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/tarawa
msgid "Pacific/Tarawa"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/tongatapu
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/tongatapu
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/tongatapu
msgid "Pacific/Tongatapu"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/truk
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/truk
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/truk
msgid "Pacific/Truk"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/wake
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/wake
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/wake
msgid "Pacific/Wake"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/wallis
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/wallis
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/wallis
msgid "Pacific/Wallis"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__pacific/yap
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__pacific/yap
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__pacific/yap
msgid "Pacific/Yap"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__parent_company_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__parent_company_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__parent_company_id
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__parent_company_id
msgid "Parent company"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_group__partner_id
msgid "Partner"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment_core.py:0
#, python-format
msgid "Partner with the same email, mobile or phone already exists: {} ({})"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__phone
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__phone
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__phone
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__phone
msgid "Phone"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment.py:0
#, python-format
msgid "Planned"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
msgid "Planned Appointments"
msgstr "المواعيد المقررة"

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__pricing_method__per_planned_duration
msgid "Planned Duration"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__report_duration_days
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__report_duration_days
msgid "Planned Duration (days)"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__report_duration_hours
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__report_duration_hours
msgid "Planned Duration (hours)"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__planned_appointment_len
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__planned_appointment_len
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__planned_appointment_len
msgid "Planned appointments"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
msgid "Planned but Already Late"
msgstr "مخطط ولكن متأخر "

#. module: business_appointment
#: code:addons/business_appointment/wizard/choose_appointment_customer.py:0
#, python-format
msgid "Please select a contact and appointments"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/slots_widget_core.js:0
#, python-format
msgid "Please try another one"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__poland
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__poland
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__poland
msgid "Poland"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_alarm__ttype__popup
msgid "Popup"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_alarm_task__notified_partner_ids
msgid "Popup is done for"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__module_business_appointment_custom_fields_website
msgid "Portal and Website Custom Fields"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__portugal
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__portugal
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__portugal
msgid "Portugal"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment_core.py:0
#: model:ir.model,name:business_appointment.model_business_appointment_core
#, python-format
msgid "Pre-Reservation"
msgstr "ما قبل الحجز"

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_appointment__state
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__state
msgid ""
"Pre-Reservation - customer is not yet defined, but time is already reserved\n"
"        Awaiting Confirmation - in case of need for customer email/phone cnfirmation or internal approval\n"
"        Processed - appointment is planned or cancelled because of time expiration"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__start_slot_datetime
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
msgid "Pre-Reservation Datetime"
msgstr ""

#. module: business_appointment
#: model:ir.ui.menu,name:business_appointment.menu_business_appointments_core
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
msgid "Pre-Reservations"
msgstr "ما قبل الحجز"

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/time_slots.xml:0
#, python-format
msgid "Price"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__pricelist_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__pricelist_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__pricelist_id
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__pricelist_id
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__pricelist_id
msgid "Pricelist"
msgstr ""

#. module: business_appointment
#: model:ir.ui.menu,name:business_appointment.menu_business_services_pricelists
msgid "Pricelists"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment_core.py:0
#, python-format
msgid "Processed"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/time_slots.xml:0
#: model:ir.model.fields,field_description:business_appointment.field_associated_product_line__product_id
#, python-format
msgid "Product"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_kanban
msgid "Product:"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/time_slots.xml:0
#: model:ir.model.fields,field_description:business_appointment.field_associated_product_line__product_uom_qty
#, python-format
msgid "Quantity"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__roc
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__roc
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__roc
msgid "ROC"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__rok
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__rok
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__rok
msgid "ROK"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_rating_rating
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__rating_ids
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_form
msgid "Rating"
msgstr "التقييم"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__rating_avg
msgid "Rating Average"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__rating_mail_template_id
msgid "Rating Email"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__rating_last_feedback
msgid "Rating Last Feedback"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__rating_last_image
msgid "Rating Last Image"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__rating_last_value
msgid "Rating Last Value"
msgstr ""

#. module: business_appointment
#: model:mail.message.subtype,description:business_appointment.mt_business_resource_rating
#: model:mail.message.subtype,description:business_appointment.mt_business_resource_type_rating
msgid "Rating Request"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__rating_count
msgid "Rating count"
msgstr ""

#. module: business_appointment
#: model:res.groups,name:business_appointment.group_business_appointment_rating
msgid "Rating for Business Appointments"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__rating_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__rating_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__rating_ids
#: model:mail.message.subtype,description:business_appointment.mt_appointment_rating
msgid "Ratings"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_form
msgid "Re-Plan"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/business_appointment_formcontroller.js:0
#, python-format
msgid "Re-schedule / Re-assign appointment"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/business_appointment_calendarcontroller.js:0
#, python-format
msgid "Re-shedule / Re-assign appointment"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_appointment__rating_last_feedback
msgid "Reason of the rating"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__recipients
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_alarm_view_search
msgid "Recipients"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__name
msgid "Reference"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__product_id
msgid "Related Product"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__pricing_method
msgid ""
"Related product sale unit of measure would be used in sales order.\n"
"        Make sure that it corresponds with appointments pricing method and duration unit of measure   \n"
"        * Per Duration - hours/days would be multiplied to related product price\n"
"        * Per Units - the price for single appointment is just a product price"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_appointment__user_id
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__user_id
#: model:ir.model.fields,help:business_appointment.field_business_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__duration
msgid "Remind Before"
msgstr ""

#. module: business_appointment
#: model:ir.ui.menu,name:business_appointment.menu_alarm_tasks
msgid "Reminder Queue"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_alarm_task
msgid "Reminder Task"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.alarm_task_view_search
msgid "Reminder Time"
msgstr ""

#. module: business_appointment
#: model:ir.ui.menu,name:business_appointment.menu_appointments_appointment_alarms
msgid "Reminders"
msgstr "التذكير"

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.alarm_task_action
msgid "Reminders Queue"
msgstr "قائمة انتظار التذكيرات"

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/time_slots.xml:0
#: code:addons/business_appointment/static/src/xml/time_slots.xml:0
#, python-format
msgid "Remove one"
msgstr ""

#. module: business_appointment
#: model:ir.ui.menu,name:business_appointment.menu_business_appointments_reports
msgid "Reporting"
msgstr "التقارير"

#. module: business_appointment
#: model:mail.message.subtype,name:business_appointment.mt_business_appointment_reserved_time_change
#: model:mail.message.subtype,name:business_appointment.mt_business_resource_reserved_time_change
#: model:mail.message.subtype,name:business_appointment.mt_business_resource_type_reserved_time_change
msgid "Rescheduling / Resource Update"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_associated_product_line__appointment_core_id
msgid "Reservation"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__allowed_from
msgid "Reservation should be done in"
msgstr "يجب أن يتم الحجز في"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__allowed_to
msgid "Reservation should not be done after"
msgstr "لا ينبغي أن يتم الحجز بعد"

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.business_appointment_core_action
msgid "Reservations"
msgstr "الحجوزات"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
msgid "Reserved Appointments"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__datetime_start
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__datetime_start
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__datetime_start
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
msgid "Reserved Time"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__datetime_end
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__datetime_end
msgid "Reserved Time End"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__resource_id
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__resource_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__resource_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__resource_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__resource_id
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__resource_ids
#: model:ir.model.fields,field_description:business_appointment.field_rating_rating__resource_id
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.rating_rating_view_search_resource_type
msgid "Resource"
msgstr "مقدم الخدمة"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__allocation_method
msgid "Resource Allocation Method"
msgstr "طريقة تخصيص الموارد"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_rating_rating__resource_id_int
msgid "Resource ID"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_kanban
msgid "Resource Image"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__resource_type
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "Resource Kind"
msgstr ""

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.rating_rating_action_business_resource
msgid "Resource Ratings"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__allocation_type
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__allocation_type
msgid "Resource Selection"
msgstr "طريقة اختيار الموارد"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__resource_type_id
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__resource_type_id
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "Resource Type"
msgstr "نوع الخدمة"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_kanban
msgid "Resource Type Image"
msgstr "صورة نوع الخدمة"

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.business_resource_type_action
#: model:ir.ui.menu,name:business_appointment.menu_business_resource_types
msgid "Resource Types"
msgstr "نوع الخدمات"

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.rating_rating_action_view_business_resource_type
msgid "Resource Types Rating"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_resource_calendar
msgid "Resource Working Time"
msgstr "فترة عمل المورد"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.rating_rating_view_search_resource_type
msgid "Resource type"
msgstr "نوع الخدمات"

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/appointment_sidebar.xml:0
#: model:ir.actions.act_window,name:business_appointment.business_resource_action
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__resource_ids
#: model:ir.ui.menu,name:business_appointment.menu_business_resources
#: model:ir.ui.menu,name:business_appointment.menu_business_resources_conf
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_kanban
#, python-format
msgid "Resources"
msgstr "مقدم الخدمات"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__user_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__user_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__user_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__user_id
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "Responsible"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__activity_user_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__activity_user_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__activity_user_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__activity_user_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/appointment_product.py:0
#, python-format
msgid "Round rule should be positive"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_alarm__ttype__sms
msgid "SMS"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__ba_approval_type__sms
msgid "SMS Confirmation"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_has_sms_error
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_has_sms_error
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_has_sms_error
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_has_sms_error
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__sms_template_id
msgid "SMS Template"
msgstr ""

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.sms_template_action
#: model:ir.ui.menu,name:business_appointment.menu_appointments_sms_templates
msgid "SMS Templates"
msgstr "قوالب الرسائل القصيرة"

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/business_appointment_formcontroller.js:0
#: code:addons/business_appointment/static/src/xml/buttons.xml:0
#, python-format
msgid "Schedule"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/business_appointment_calendarcontroller.js:0
#: model:ir.model,name:business_appointment.model_make_business_appointment
#, python-format
msgid "Schedule Appointment"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__schedule_datetime
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__schedule_datetime
msgid "Schedule Datetime"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/business_appointment_formcontroller.js:0
#: code:addons/business_appointment/static/src/js/business_appointment_list_controller.js:0
#, python-format
msgid "Schedule appointment"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/alarm_task.py:0
#, python-format
msgid "Scheduled time: {};{}{}{}"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__date_start
msgid "Search in dates"
msgstr "البحث في التواريخ"

#. module: business_appointment
#: model:ir.actions.server,name:business_appointment.cron_reminder_alarm_task_ir_actions_server
#: model:ir.cron,cron_name:business_appointment.cron_reminder_alarm_task
#: model:ir.cron,name:business_appointment.cron_reminder_alarm_task
msgid "Send SMS and Email Reminders"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__ba_auto_sale_order__sent
msgid "Sent Quotation (ready to accept and pay)"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__sequence
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__sequence
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__sequence
msgid "Sequence"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_appointment_product
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__service_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__service_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__service_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__always_service_id
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__service_id
#: model:ir.model.fields,field_description:business_appointment.field_rating_rating__service_id
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.rating_rating_view_search_resource_type
msgid "Service"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_rating_rating__service_id_int
msgid "Service ID"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_kanban
msgid "Service Image"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__service_method
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__service_method
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
msgid "Service Method"
msgstr "طريقة الخدمة"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
msgid "Service Method: Multiple"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
msgid "Service Method: Single"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__name
msgid "Service Name"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__product_price
msgid "Service Price"
msgstr ""

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.rating_rating_action_appointment_product
msgid "Service Ratings"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__service_method__single
msgid "Service is always the same for all resources and appointments"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__make_services_visible
msgid "Service is visible"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__service_method__multiple
msgid "Service should be selected for resources and for appointment"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/appointment_sidebar.xml:0
#: model:ir.actions.act_window,name:business_appointment.appointment_product_action
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__final_service_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__final_service_ids
#: model:ir.ui.menu,name:business_appointment.menu_business_services
#, python-format
msgid "Services"
msgstr "خدمات"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_form
msgid "Services and Resources"
msgstr ""

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.res_config_settings_business_appointment_action
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_form
msgid "Settings"
msgstr "إعدادات"

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__singapore
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__singapore
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__singapore
msgid "Singapore"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/ba_popups.js:0
#, python-format
msgid "Snooze"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment_core.py:0
#, python-format
msgid "Sorry, this appointment might not be confirmed due to its expiration"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/models/business_appointment_core.py:0
#: code:addons/business_appointment/static/src/js/slots_widget_core.js:0
#, python-format
msgid "Sorry, this time slot has been just reserved"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/js/slots_widget_core.js:0
#, python-format
msgid ""
"Sorry, you can not schedule more appointments: the maximum number is reached"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__state
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__state
msgid "Stage"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_tree
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_tree
msgid "Start"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
msgid "Start Date"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__start_round_rule
msgid "Start Round"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__start_round_rule_days
msgid "Start Time"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/appointment_product.py:0
#, python-format
msgid "Start time should be within 0:00 and 23:59"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_analytic__state
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__state_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__state_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__state_id
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__state_id
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_analytic_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.choose_appointment_customert_form_view
msgid "State"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/appointment_sidebar.xml:0
#, python-format
msgid "States"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__activity_state
#: model:ir.model.fields,help:business_appointment.field_business_appointment__activity_state
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__activity_state
#: model:ir.model.fields,help:business_appointment.field_business_resource__activity_state
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__street
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__street
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__street
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__street
msgid "Street"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.choose_appointment_customert_form_view
msgid "Street 2..."
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.choose_appointment_customert_form_view
msgid "Street..."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__street2
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__street2
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__street2
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__street2
msgid "Street2"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__success_mail_template_id
msgid "Success Email"
msgstr "بريد النجاح"

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_resource__sucess_email_partner_ids
msgid ""
"Success email would be also sent for those partners as a copy of a client "
"success email"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__sucess_email_partner_ids
msgid "Success emails CC"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__appointments
msgid "Tech Field"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__template_name
msgid "Template"
msgstr ""

#. module: business_appointment
#: model:ir.actions.act_window,name:business_appointment.action_mail_template_ba
msgid "Templates"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_contact_info__partner_name
#: model:ir.model.fields,help:business_appointment.field_business_appointment__partner_name
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__partner_name
#: model:ir.model.fields,help:business_appointment.field_choose_appointment_customer__partner_name
msgid ""
"The name of the company which would be created when appointment is confirmed\n"
"        Leave it empty if a contact is an individual"
msgstr ""

#. module: business_appointment
#: model_terms:ir.actions.act_window,help:business_appointment.rating_rating_action_view_business_resource_type
msgid "There is no rating for this appointment at the moment"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
msgid "This Month"
msgstr "هذا الشهر"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
msgid "This Year"
msgstr "هذه السنة"

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__tz
#: model:ir.model.fields,help:business_appointment.field_business_resource__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/alarm_task.py:0
#, python-format
msgid "This is the SMS sent to {}: <br/>"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_res_config_settings__ba_max_multi_scheduling
msgid ""
"This setting is applied only to backend. \n"
"        For portal and website maximum number, please look at website specific options"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__date_end
msgid "Till"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_form
msgid "Time Requirements"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_make_business_appointment__timeslots
msgid "Time Slots"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__module_business_appointment_time_tracking
msgid "Time Tracking"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
msgid "Timeline"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__tz
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__tz
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__tz
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__tz
msgid "Timezone"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__title
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__title
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__title
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__title
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.choose_appointment_customert_form_view
msgid "Title"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__allowed_to_uom
msgid "To UoM"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
msgid "Today"
msgstr "اليوم"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_search
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "Today Activities"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__turkey
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__turkey
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__turkey
msgid "Turkey"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"Turn on to add custom fields for appointments, resource types, resources, and \n"
"                                    services. The tool"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Turn on to create sale orders for appointments. The tool"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Turn on to let portal or/and website visitors to schedule appointments"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"Turn on to link HR employees to resources and use their work time and leaves. The\n"
"                                    tool"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"Turn on to link portal sale order with appointments and to have a possibility to \n"
"                                    show service prices and pricelists on appointment pages. The tool"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Turn on to log actual time spent on appointment"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"Turn on to offer managers complementary products while scheduling a time slot.\n"
"                                    Such products would be shown if any are associated with reserved service"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__rating_option
msgid "Turn on to send rating request when appointment is marked done"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"Turn on to send rating requests when appointments are done. This option might be \n"
"                                    turned on or off per each resource type"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"Turn on to show custom fields for portal and website visitors. The tool"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_alarm__ttype
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__resource_type_id
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__resource_type_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__resource_type_id
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_alarm_view_search
msgid "Type"
msgstr "نوع"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__type_available_service_ids
msgid "Type Services"
msgstr "نوع الخدمة"

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__activity_exception_decoration
#: model:ir.model.fields,help:business_appointment.field_business_appointment__activity_exception_decoration
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__activity_exception_decoration
#: model:ir.model.fields,help:business_appointment.field_business_resource__activity_exception_decoration
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/appointment_sidebar.xml:0
#, python-format
msgid "Types"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__uct
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__uct
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__uct
msgid "UCT"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/alaska
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/alaska
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/alaska
msgid "US/Alaska"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/aleutian
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/aleutian
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/aleutian
msgid "US/Aleutian"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/arizona
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/arizona
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/arizona
msgid "US/Arizona"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/central
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/central
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/central
msgid "US/Central"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/east-indiana
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/east-indiana
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/east-indiana
msgid "US/East-Indiana"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/eastern
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/eastern
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/eastern
msgid "US/Eastern"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/hawaii
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/hawaii
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/hawaii
msgid "US/Hawaii"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/indiana-starke
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/indiana-starke
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/indiana-starke
msgid "US/Indiana-Starke"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/michigan
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/michigan
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/michigan
msgid "US/Michigan"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/mountain
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/mountain
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/mountain
msgid "US/Mountain"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/pacific
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/pacific
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/pacific
msgid "US/Pacific"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__us/samoa
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__us/samoa
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__us/samoa
msgid "US/Samoa"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__utc
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__utc
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__utc
msgid "UTC"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "Unassigned"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/time_slots.xml:0
#, python-format
msgid "Unit Price"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__pricing_method__per_unit
msgid "Units"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__universal
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__universal
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__universal
msgid "Universal"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Universal Appointments: Custom Fields"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Universal Appointments: Custom Fields for Website and Portal"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Universal Appointments: HR Bridge"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Universal Appointments: Portal and Website"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Universal Appointments: Sales"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Universal Appointments: Time Tracking"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Universal Appointments: Website Sales"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_unread
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_unread
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_unread
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_unread
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_unread
msgid "Unread Messages"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__message_unread_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__message_unread_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__message_unread_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__message_unread_counter
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__group_business_appointment_rating
msgid "Use Rating for Appointments"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_ir_ui_view
msgid "View"
msgstr "أداة العرض"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_ir_ui_view__type
#: model:ir.model.fields,field_description:business_appointment.field_website_page__type
msgid "View Type"
msgstr "نوع العرض "

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__w-su
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__w-su
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__w-su
msgid "W-SU"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__wet
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__wet
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__wet
msgid "WET"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__ba_approval_type
msgid "Website / Portal Confirmation"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__website_message_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__website_message_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__website_message_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__website_message_ids
#: model:ir.model.fields,field_description:business_appointment.field_business_resource_type__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_res_config_settings__module_business_appointment_website_sale
msgid "Website Sales"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid "Website Specific Options"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,help:business_appointment.field_appointment_product__website_message_ids
#: model:ir.model.fields,help:business_appointment.field_business_appointment__website_message_ids
#: model:ir.model.fields,help:business_appointment.field_business_appointment_core__website_message_ids
#: model:ir.model.fields,help:business_appointment.field_business_resource__website_message_ids
#: model:ir.model.fields,help:business_appointment.field_business_resource_type__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"When a user selects a time slot it becomes unavailable for other reservation.\n"
"                                    However, if scheduling is not complete, CBMS should unblock this time slot. Define\n"
"                                    when unblocking should take place"
msgstr ""

#. module: business_appointment
#: model:ir.model,name:business_appointment.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "تفاصيل العمل"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_search
msgid "Working Calendar"
msgstr "اوقات العمل"

#. module: business_appointment
#: model:ir.ui.menu,name:business_appointment.menu_working_calendar
msgid "Working Calendars"
msgstr "اوقات العمل"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_product__resource_calendar_id
#: model:ir.model.fields,field_description:business_appointment.field_business_resource__resource_calendar_id
msgid "Working Hours"
msgstr "ساعات العمل"

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__day_year
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__day_year
msgid "Year"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.choose_appointment_customert_form_view
msgid "ZIP"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields,field_description:business_appointment.field_appointment_contact_info__zipcode
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment__zipcode
#: model:ir.model.fields,field_description:business_appointment.field_business_appointment_core__zipcode
#: model:ir.model.fields,field_description:business_appointment.field_choose_appointment_customer__zipcode
msgid "Zip"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment__tz__zulu
#: model:ir.model.fields.selection,name:business_appointment.selection__business_appointment_core__tz__zulu
#: model:ir.model.fields.selection,name:business_appointment.selection__res_config_settings__appoin_comp_tz__zulu
msgid "Zulu"
msgstr ""

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/resource_many2many.xml:0
#, python-format
msgid "any"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_product__duration_uom__days
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allowed_from_uom__days
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allowed_to_uom__days
msgid "days"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_form
msgid "description"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.choose_appointment_customert_form_view
msgid "extra notes..."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__appointment_product__duration_uom__hours
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allowed_from_uom__hours
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allowed_to_uom__hours
msgid "hours"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"is required (distributed free).\n"
"                                    The app 'Sales' would be automatically installed in your database."
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"is required (free if Universal Appointments: Portal and \n"
"                                    Universal Appointments: Custom Fields are installed)"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.res_config_settings_view_form
msgid ""
"is required (free if Universal Appointments: Portal and \n"
"                                    Website and Universal Appointments are installed).\n"
"                                    The app 'eCommerce' would be automatically installed in your database."
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allowed_from_uom__minutes
msgid "minutes"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allowed_from_uom__months
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allowed_to_uom__months
msgid "months"
msgstr ""

#. module: business_appointment
#: model:ir.actions.report,print_report_name:business_appointment.action_report_business_appointment
msgid "object.name"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.appointment_product_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_form
msgid "resource reference"
msgstr "اسم مقدم الخدمة"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_form
msgid "resource type reference"
msgstr "اسم الخدمة"

#. module: business_appointment
#. openerp-web
#: code:addons/business_appointment/static/src/xml/appointment_sidebar.xml:0
#, python-format
msgid "save as default"
msgstr "إحفظ كافتراضي"

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_type_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_resource_view_form
msgid "short description"
msgstr ""

#. module: business_appointment
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_core_view_form
#: model_terms:ir.ui.view,arch_db:business_appointment.business_appointment_view_form
msgid "to"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allowed_from_uom__weeks
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allowed_to_uom__weeks
msgid "weeks"
msgstr ""

#. module: business_appointment
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allowed_from_uom__years
#: model:ir.model.fields.selection,name:business_appointment.selection__business_resource_type__allowed_to_uom__years
msgid "years"
msgstr ""

#. module: business_appointment
#: model:mail.template,subject:business_appointment.email_template_default_reminder
msgid "{{ ctx['target_company'].name }}: Appointment Reminder"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/business_appointment.py:0
#, python-format
msgid "{} for {} by {}"
msgstr ""

#. module: business_appointment
#: code:addons/business_appointment/models/appointment_product.py:0
#, python-format
msgid "{} per {}"
msgstr ""
