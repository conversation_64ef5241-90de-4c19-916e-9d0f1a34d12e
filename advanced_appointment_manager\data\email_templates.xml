<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="email_template_appointment_approved_aam" model="mail.template">
            <field name="name">Appointment Approved Notification</field>
            <field name="model_id" eval="ref('calendar.model_calendar_event')"/>
            <field name="subject">[Approved] Your appointment for ${object.name}</field>
            <field name="email_from">${(object.user_id.email_formatted or user.email_formatted or '<EMAIL>')|safe}</field>
            <field name="partner_to">${object.partner_id.id}</field>
            <field name="body_html" type="html">
                <div style="font-family: sans-serif; font-size: 14px;">
                    <p>Dear ${object.partner_id.name},</p>
                    <p>
                        Your appointment for <strong>${object.name}</strong> 
                        on <strong><t t-out="format_datetime(object.start, tz=object.partner_id.tz or 'UTC')"/></strong>
                        has been <strong style="color:green;">APPROVED</strong>.
                    </p>
                    <p>We look forward to seeing you.</p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="email_template_appointment_rejected_aam" model="mail.template">
            <field name="name">Appointment Rejected Notification</field>
            <field name="model_id" eval="ref('calendar.model_calendar_event')"/>
            <field name="subject">[Rejected] Your appointment for ${object.name}</field>
            <field name="email_from">${(object.user_id.email_formatted or user.email_formatted or '<EMAIL>')|safe}</field>
            <field name="partner_to">${object.partner_id.id}</field>
            <field name="body_html" type="html">
                <div style="font-family: sans-serif; font-size: 14px;">
                    <p>Dear ${object.partner_id.name},</p>
                    <p>
                        We regret to inform you that your appointment request for <strong>${object.name}</strong> 
                        on <strong><t t-out="format_datetime(object.start, tz=object.partner_id.tz or 'UTC')"/></strong>
                        has been <strong style="color:red;">REJECTED</strong>.
                    </p>
                    <p>Please contact us if you have any questions.</p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="email_template_appointment_forfeited_aam" model="mail.template">
            <field name="name">Appointment Forfeited Notification</field>
            <field name="model_id" eval="ref('calendar.model_calendar_event')"/>
            <field name="subject">[Forfeited] Your appointment for ${object.name}</field>
            <field name="email_from">${(object.user_id.email_formatted or user.email_formatted or '<EMAIL>')|safe}</field>
            <field name="partner_to">${object.partner_id.id}</field>
            <field name="body_html" type="html">
                <div style="font-family: sans-serif; font-size: 14px;">
                    <p>Dear ${object.partner_id.name},</p>
                    <p>
                        This is to inform you that your appointment for <strong>${object.name}</strong> 
                        on <strong><t t-out="format_datetime(object.start, tz=object.partner_id.tz or 'UTC')"/></strong>
                        has been <strong style="color:orange;">FORFEITED</strong> due to a failure to check in by the required time.
                    </p>
                    <p>Please contact us if you wish to re-book.</p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>